<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR智能识别 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
            min-height: 100vh;
        }
        .feature-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .hero-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-nav {
            background: rgba(17, 24, 39, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        .feature-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 导航栏 -->
    <nav class="glass-nav text-white p-6 shadow-2xl">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-3xl font-bold flex items-center">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-search-plus text-sky-600 text-xl"></i>
                </div>
                OCR智能识别
            </h1>
            <div class="flex items-center space-x-4">
                <div class="hidden md:flex items-center space-x-2 text-sm">
                    <i class="fas fa-shield-alt text-green-300"></i>
                    <span>安全可靠</span>
                </div>
                <!-- 未来可添加用户登录/注册链接 -->
                <!-- <a href="#" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">登录</a> -->
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="container mx-auto px-6 py-6">
        <!-- 英雄区域 - 优化版本 -->
        <section class="hero-section text-center mb-12 p-8 rounded-3xl">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                    智能OCR识别
                </h2>
                <p class="text-xl md:text-2xl font-light text-blue-100 mb-6 leading-relaxed">
                    让文字提取变得简单
                </p>
                <p class="text-base text-blue-200 mb-8 max-w-2xl mx-auto leading-relaxed opacity-90">
                    采用先进的人工智能技术，为您提供高精度、快速的图片文字识别服务
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center text-sm bg-white bg-opacity-10 rounded-2xl p-4 backdrop-blur-sm">
                    <div class="flex items-center text-blue-100">
                        <i class="fas fa-check-circle mr-2 text-green-300"></i>
                        <span class="font-medium">99%+ 识别准确率</span>
                    </div>
                    <div class="hidden sm:block w-px h-4 bg-blue-300 opacity-50"></div>
                    <div class="flex items-center text-blue-100">
                        <i class="fas fa-bolt mr-2 text-yellow-300"></i>
                        <span class="font-medium">秒级处理速度</span>
                    </div>
                    <div class="hidden sm:block w-px h-4 bg-blue-300 opacity-50"></div>
                    <div class="flex items-center text-blue-100">
                        <i class="fas fa-globe mr-2 text-blue-300"></i>
                        <span class="font-medium">多语言支持</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能模块卡片 -->
        <section class="mb-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-10">
                <!-- 功能模块 1: 在线识别 -->
                <a href="single_recognition.html" class="feature-card p-8 rounded-2xl text-center block group transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-2xl">
                    <div class="feature-icon mb-6 transform group-hover:scale-110 transition-transform duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-2 shadow-lg">
                            <i class="fas fa-eye fa-2x text-white"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">在线识别</h3>
                    <p class="text-gray-600 mb-4 text-base leading-relaxed">
                        上传单张或少量图片，快速获取识别结果
                    </p>
                    <div class="flex justify-center items-center space-x-4 text-sm text-gray-500">
                        <span class="flex items-center bg-green-50 px-3 py-1 rounded-full">
                            <i class="fas fa-clock mr-1 text-green-500"></i>
                            即时处理
                        </span>
                        <span class="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                            <i class="fas fa-images mr-1 text-blue-500"></i>
                            多格式
                        </span>
                    </div>
                </a>

                <!-- 功能模块 2: 批量任务 -->
                <a href="batch_recognition.html" class="feature-card p-8 rounded-2xl text-center block group transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-2xl">
                    <div class="feature-icon mb-6 transform group-hover:scale-110 transition-transform duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-2 shadow-lg">
                            <i class="fas fa-copy fa-2x text-white"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">批量任务识别</h3>
                    <p class="text-gray-600 mb-4 text-base leading-relaxed">
                        处理大量图片文件，系统后台异步处理
                    </p>
                    <div class="flex justify-center items-center space-x-4 text-sm text-gray-500">
                        <span class="flex items-center bg-purple-50 px-3 py-1 rounded-full">
                            <i class="fas fa-tasks mr-1 text-purple-500"></i>
                            任务管理
                        </span>
                        <span class="flex items-center bg-indigo-50 px-3 py-1 rounded-full">
                            <i class="fas fa-download mr-1 text-indigo-500"></i>
                            批量导出
                        </span>
                    </div>
                </a>

                <!-- 功能模块 3: 结果管理 -->
                <a href="results_management.html" class="feature-card p-8 rounded-2xl text-center block group transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-2xl">
                    <div class="feature-icon mb-6 transform group-hover:scale-110 transition-transform duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-2 shadow-lg">
                            <i class="fas fa-folder-open fa-2x text-white"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">结果管理</h3>
                    <p class="text-gray-600 mb-4 text-base leading-relaxed">
                        查看、编辑和管理所有识别结果
                    </p>
                    <div class="flex justify-center items-center space-x-4 text-sm text-gray-500">
                        <span class="flex items-center bg-indigo-50 px-3 py-1 rounded-full">
                            <i class="fas fa-search mr-1 text-indigo-500"></i>
                            智能搜索
                        </span>
                        <span class="flex items-center bg-green-50 px-3 py-1 rounded-full">
                            <i class="fas fa-edit mr-1 text-green-500"></i>
                            在线编辑
                        </span>
                    </div>
                </a>
            </div>

            <!-- 服务选择标题 - 移到下方 -->
            <div class="text-center mt-12">
                <h3 class="text-3xl md:text-4xl font-bold text-white mb-3">选择您的服务</h3>
                <p class="text-lg text-blue-200 opacity-90">多种识别方式，满足不同需求</p>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg text-white text-center p-6">
        <div class="container mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-sky-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search-plus text-white"></i>
                    </div>
                    <span class="text-xl font-bold">OCR智能识别</span>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="#" class="hover:text-sky-400 transition-colors">使用条款</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">隐私政策</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">联系我们</a>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-6">
                <p class="text-gray-300">&copy; 2025 OCR智能识别平台-Design By XQJ. 保留所有权利.</p>
                <p class="text-sm text-gray-400 mt-2">技术支持：XQJ | 让AI为您的工作赋能</p>
            </div>
        </div>
    </footer>

</body>
</html>