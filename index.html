<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR智能识别 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
            min-height: 100vh;
        }
        .feature-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .hero-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-nav {
            background: rgba(17, 24, 39, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        .feature-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 导航栏 -->
    <nav class="glass-nav text-white p-6 shadow-2xl">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-3xl font-bold flex items-center">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-search-plus text-sky-600 text-xl"></i>
                </div>
                OCR智能识别
            </h1>
            <div class="flex items-center space-x-4">
                <div class="hidden md:flex items-center space-x-2 text-sm">
                    <i class="fas fa-shield-alt text-green-300"></i>
                    <span>安全可靠</span>
                </div>
                <!-- 未来可添加用户登录/注册链接 -->
                <!-- <a href="#" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">登录</a> -->
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="container mx-auto mt-8 p-6">
        <!-- 英雄区域 -->
        <section class="hero-section text-center mb-16 p-12 rounded-3xl">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
                    智能OCR识别
                    <span class="block text-3xl md:text-4xl font-normal text-blue-100 mt-2">让文字提取变得简单</span>
                </h2>
                <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
                    采用先进的人工智能技术，为您提供高精度、快速的图片文字识别服务。支持多种格式，批量处理，让工作更高效。
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <div class="flex items-center text-blue-100">
                        <i class="fas fa-check-circle mr-2 text-green-300"></i>
                        <span>99%+ 识别准确率</span>
                    </div>
                    <div class="flex items-center text-blue-100">
                        <i class="fas fa-bolt mr-2 text-yellow-300"></i>
                        <span>秒级处理速度</span>
                    </div>
                    <div class="flex items-center text-blue-100">
                        <i class="fas fa-globe mr-2 text-blue-300"></i>
                        <span>多语言支持</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能模块卡片 -->
        <section class="mb-16">
            <h3 class="text-3xl font-bold text-center text-white mb-12">选择您的服务</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                <!-- 功能模块 1: 在线识别 -->
                <a href="single_recognition.html" class="feature-card p-10 rounded-2xl text-center block group">
                    <div class="feature-icon mb-6 transform group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-file-image fa-4x"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-800">在线识别</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        上传单张或少量图片，快速获取识别结果。支持JPG、PNG、BMP等多种格式，实时预览识别内容。
                    </p>
                    <div class="flex justify-center items-center space-x-4 text-sm text-gray-500">
                        <span class="flex items-center">
                            <i class="fas fa-clock mr-1 text-green-500"></i>
                            即时处理
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-images mr-1 text-blue-500"></i>
                            多格式支持
                        </span>
                    </div>
                </a>

                <!-- 功能模块 2: 批量任务 -->
                <a href="batch_recognition.html" class="feature-card p-10 rounded-2xl text-center block group">
                    <div class="feature-icon mb-6 transform group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-folder-open fa-4x"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-800">批量任务识别</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        处理大量图片文件，创建识别任务，系统后台异步处理。支持任务管理、进度跟踪和结果导出。
                    </p>
                    <div class="flex justify-center items-center space-x-4 text-sm text-gray-500">
                        <span class="flex items-center">
                            <i class="fas fa-tasks mr-1 text-purple-500"></i>
                            任务管理
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-download mr-1 text-indigo-500"></i>
                            批量导出
                        </span>
                    </div>
                </a>

                <!-- 功能模块 3: 结果管理 -->
                <a href="results_management.html" class="feature-card p-10 rounded-2xl text-center block group">
                    <div class="feature-icon mb-6 transform group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line fa-4x"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-gray-800">结果管理</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        查看、编辑和管理所有识别结果。支持智能搜索、分类筛选和批量导出功能。
                    </p>
                    <div class="flex justify-center items-center space-x-4 text-sm text-gray-500">
                        <span class="flex items-center">
                            <i class="fas fa-search mr-1 text-indigo-500"></i>
                            智能搜索
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-edit mr-1 text-green-500"></i>
                            在线编辑
                        </span>
                    </div>
                </a>
            </div>
        </section>

        <!-- 应用特色 -->
        <section class="hero-section p-10 rounded-3xl">
            <h3 class="text-3xl font-bold text-white mb-12 text-center">为什么选择我们</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center max-w-4xl mx-auto">
                <div class="p-6 rounded-2xl bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border border-white border-opacity-20">
                    <div class="text-green-400 mb-4"><i class="fas fa-check-circle fa-3x"></i></div>
                    <h4 class="font-bold text-xl text-white mb-3">高精度识别</h4>
                    <p class="text-blue-100 leading-relaxed">采用最新深度学习算法，识别准确率高达99%以上，支持中英文及多种语言混合识别。</p>
                </div>
                <div class="p-6 rounded-2xl bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border border-white border-opacity-20">
                    <div class="text-yellow-400 mb-4"><i class="fas fa-rocket fa-3x"></i></div>
                    <h4 class="font-bold text-xl text-white mb-3">闪电处理</h4>
                    <p class="text-blue-100 leading-relaxed">云端GPU加速处理，单张图片识别仅需1-3秒，批量处理效率提升10倍以上。</p>
                </div>
                <div class="p-6 rounded-2xl bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border border-white border-opacity-20">
                    <div class="text-blue-400 mb-4"><i class="fas fa-shield-alt fa-3x"></i></div>
                    <h4 class="font-bold text-xl text-white mb-3">安全可靠</h4>
                    <p class="text-blue-100 leading-relaxed">企业级数据加密保护，处理完成后自动删除临时文件，确保您的隐私安全。</p>
                </div>
            </div>
        </section>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg text-white text-center p-8 mt-16">
        <div class="container mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-sky-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search-plus text-white"></i>
                    </div>
                    <span class="text-xl font-bold">OCR智能识别</span>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="#" class="hover:text-sky-400 transition-colors">使用条款</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">隐私政策</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">联系我们</a>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-6">
                <p class="text-gray-300">&copy; 2025 OCR智能识别平台-Design By XQJ. 保留所有权利.</p>
                <p class="text-sm text-gray-400 mt-2">技术支持：XQJ | 让AI为您的工作赋能</p>
            </div>
        </div>
    </footer>

</body>
</html>