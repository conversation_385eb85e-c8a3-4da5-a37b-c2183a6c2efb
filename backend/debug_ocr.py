#!/usr/bin/env python3
"""
调试 PaddleOCR 输出格式的脚本
"""

from paddleocr import PaddleOCR
import json

def debug_paddleocr():
    """调试 PaddleOCR 的输出格式"""
    print("初始化 PaddleOCR...")
    ocr = PaddleOCR(use_angle_cls=True, lang='ch')
    
    print("识别测试图片...")
    result = ocr.predict('test_image.jpg')
    
    print(f"结果类型: {type(result)}")
    print(f"结果长度: {len(result) if result else 0}")
    
    if result:
        print(f"第一个元素类型: {type(result[0])}")
        print(f"第一个元素: {result[0]}")
        
        if result[0]:
            print(f"第一行类型: {type(result[0][0])}")
            print(f"第一行: {result[0][0]}")
            
            if len(result[0][0]) >= 2:
                bbox = result[0][0][0]
                text_info = result[0][0][1]
                print(f"bbox 类型: {type(bbox)}")
                print(f"bbox: {bbox}")
                print(f"text_info 类型: {type(text_info)}")
                print(f"text_info: {text_info}")
    
    print("\n完整结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    debug_paddleocr()
