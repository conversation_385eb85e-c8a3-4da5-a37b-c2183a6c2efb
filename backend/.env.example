# OCR智能识别平台环境配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用基础配置
APP_NAME=OCR智能识别平台
APP_VERSION=1.0.0
DEBUG=false

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./ocr_platform.db
# 如果使用PostgreSQL，格式如下：
# DATABASE_URL=postgresql://username:password@localhost:5432/ocr_platform
# 如果使用MySQL，格式如下：
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/ocr_platform

# Redis配置（用于任务队列）
REDIS_URL=redis://localhost:6379/0

# 文件存储配置
UPLOAD_DIR=./uploads
RESULTS_DIR=./results
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=.jpg,.jpeg,.png,.bmp,.webp,.pdf

# OCR配置
OCR_USE_GPU=false
OCR_USE_ANGLE_CLS=true
OCR_USE_SPACE_CHAR=true
OCR_LANG=ch

# 任务配置
MAX_BATCH_SIZE=100
TASK_TIMEOUT=3600  # 1小时

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
