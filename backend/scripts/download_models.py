#!/usr/bin/env python3
"""
PaddleOCR模型预下载脚本
一次性下载所有需要的模型，避免每次启动时重新下载
"""

import os
import sys
import time
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )
    
    # 添加文件日志
    log_file = project_root / "logs" / "model_download.log"
    log_file.parent.mkdir(exist_ok=True)
    logger.add(
        str(log_file),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
        level="DEBUG",
        rotation="10 MB"
    )

def check_model_cache():
    """检查模型缓存目录"""
    cache_dir = Path.home() / ".paddlex" / "official_models"
    logger.info(f"模型缓存目录: {cache_dir}")
    
    if cache_dir.exists():
        models = list(cache_dir.glob("*"))
        logger.info(f"已缓存模型数量: {len(models)}")
        for model in models:
            if model.is_dir():
                logger.info(f"  - {model.name}")
        return len(models) > 0
    else:
        logger.info("模型缓存目录不存在")
        return False

def download_models():
    """下载PaddleOCR模型"""
    logger.info("开始下载PaddleOCR模型...")
    
    try:
        # 导入PaddleOCR（这会触发模型下载）
        from paddleocr import PaddleOCR
        
        logger.info("初始化PaddleOCR实例...")
        start_time = time.time()
        
        # 创建OCR实例，这会自动下载所需模型
        ocr = PaddleOCR(
            use_textline_orientation=True,  # 使用文本行方向检测
            lang='ch'  # 中文
        )
        
        download_time = time.time() - start_time
        logger.success(f"模型下载完成！耗时: {download_time:.2f}秒")
        
        # 测试模型是否正常工作
        logger.info("测试模型功能...")
        test_image_path = project_root / "test_image.jpg"
        
        if test_image_path.exists():
            logger.info(f"使用测试图片: {test_image_path}")
            result = ocr.ocr(str(test_image_path))
            if result and len(result) > 0:
                logger.success("模型测试成功！")
                
                # 显示识别结果
                page_result = result[0]
                if hasattr(page_result, '__getitem__'):
                    try:
                        rec_texts = page_result['rec_texts']
                        if rec_texts:
                            logger.info(f"测试识别结果: {rec_texts[:3]}...")  # 显示前3个结果
                    except:
                        pass
            else:
                logger.warning("模型测试未返回结果，但下载成功")
        else:
            logger.warning(f"测试图片不存在: {test_image_path}")
            logger.info("模型下载成功，跳过功能测试")
        
        return True
        
    except Exception as e:
        logger.error(f"模型下载失败: {str(e)}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 50)
    logger.info("PaddleOCR模型预下载工具")
    logger.info("=" * 50)
    
    # 检查现有模型
    has_models = check_model_cache()
    
    if has_models:
        logger.info("检测到已缓存的模型")
        user_input = input("是否重新下载模型？(y/N): ").strip().lower()
        if user_input not in ['y', 'yes']:
            logger.info("跳过模型下载")
            return
    
    # 下载模型
    logger.info("开始下载模型，请耐心等待...")
    success = download_models()
    
    if success:
        logger.success("✅ 模型下载完成！")
        logger.info("现在启动OCR服务将会很快，无需等待模型下载")
        
        # 再次检查缓存
        check_model_cache()
    else:
        logger.error("❌ 模型下载失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
