Metadata-Version: 2.4
Name: GPUtil
Version: 1.4.0
Summary: GPUtil is a Python module for getting the GPU status from NVIDA GPUs using nvidia-smi.
Home-page: https://github.com/anderskm/gputil
Download-URL: https://github.com/anderskm/gputil/tarball/v1.4.0
Author: <PERSON>
Author-email: andersk<PERSON><EMAIL>
License: MIT
Keywords: gpu,utilization,load,memory,available,usage,free,select,nvidia
Dynamic: author
Dynamic: author-email
Dynamic: download-url
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: summary
