from numpy._pytesttester import PytestTester

from numpy.ma import extras as extras

from numpy.ma.core import (
    MAError as MA<PERSON>rror,
    Mask<PERSON>rror as <PERSON><PERSON>rror,
    MaskType as MaskType,
    MaskedArray as MaskedArray,
    abs as abs,
    absolute as absolute,
    add as add,
    all as all,
    allclose as allclose,
    allequal as allequal,
    alltrue as alltrue,
    amax as amax,
    amin as amin,
    angle as angle,
    anom as anom,
    anomalies as anomalies,
    any as any,
    append as append,
    arange as arange,
    arccos as arccos,
    arccosh as arccosh,
    arcsin as arcsin,
    arcsinh as arcsinh,
    arctan as arctan,
    arctan2 as arctan2,
    arctanh as arctanh,
    argmax as argmax,
    argmin as argmin,
    argsort as argsort,
    around as around,
    array as array,
    asanyarray as asanyarray,
    asarray as asarray,
    bitwise_and as bitwise_and,
    bitwise_or as bitwise_or,
    bitwise_xor as bitwise_xor,
    bool as bool,
    ceil as ceil,
    choose as choose,
    clip as clip,
    common_fill_value as common_fill_value,
    compress as compress,
    compressed as compressed,
    concatenate as concatenate,
    conjugate as conjugate,
    convolve as convolve,
    copy as copy,
    correlate as correlate,
    cos as cos,
    cosh as cosh,
    count as count,
    cumprod as cumprod,
    cumsum as cumsum,
    default_fill_value as default_fill_value,
    diag as diag,
    diagonal as diagonal,
    diff as diff,
    divide as divide,
    empty as empty,
    empty_like as empty_like,
    equal as equal,
    exp as exp,
    expand_dims as expand_dims,
    fabs as fabs,
    filled as filled,
    fix_invalid as fix_invalid,
    flatten_mask as flatten_mask,
    flatten_structured_array as flatten_structured_array,
    floor as floor,
    floor_divide as floor_divide,
    fmod as fmod,
    frombuffer as frombuffer,
    fromflex as fromflex,
    fromfunction as fromfunction,
    getdata as getdata,
    getmask as getmask,
    getmaskarray as getmaskarray,
    greater as greater,
    greater_equal as greater_equal,
    harden_mask as harden_mask,
    hypot as hypot,
    identity as identity,
    ids as ids,
    indices as indices,
    inner as inner,
    innerproduct as innerproduct,
    isMA as isMA,
    isMaskedArray as isMaskedArray,
    is_mask as is_mask,
    is_masked as is_masked,
    isarray as isarray,
    left_shift as left_shift,
    less as less,
    less_equal as less_equal,
    log as log,
    log10 as log10,
    log2 as log2,
    logical_and as logical_and,
    logical_not as logical_not,
    logical_or as logical_or,
    logical_xor as logical_xor,
    make_mask as make_mask,
    make_mask_descr as make_mask_descr,
    make_mask_none as make_mask_none,
    mask_or as mask_or,
    masked as masked,
    masked_array as masked_array,
    masked_equal as masked_equal,
    masked_greater as masked_greater,
    masked_greater_equal as masked_greater_equal,
    masked_inside as masked_inside,
    masked_invalid as masked_invalid,
    masked_less as masked_less,
    masked_less_equal as masked_less_equal,
    masked_not_equal as masked_not_equal,
    masked_object as masked_object,
    masked_outside as masked_outside,
    masked_print_option as masked_print_option,
    masked_singleton as masked_singleton,
    masked_values as masked_values,
    masked_where as masked_where,
    max as max,
    maximum as maximum,
    maximum_fill_value as maximum_fill_value,
    mean as mean,
    min as min,
    minimum as minimum,
    minimum_fill_value as minimum_fill_value,
    mod as mod,
    multiply as multiply,
    mvoid as mvoid,
    ndim as ndim,
    negative as negative,
    nomask as nomask,
    nonzero as nonzero,
    not_equal as not_equal,
    ones as ones,
    outer as outer,
    outerproduct as outerproduct,
    power as power,
    prod as prod,
    product as product,
    ptp as ptp,
    put as put,
    putmask as putmask,
    ravel as ravel,
    remainder as remainder,
    repeat as repeat,
    reshape as reshape,
    resize as resize,
    right_shift as right_shift,
    round as round,
    set_fill_value as set_fill_value,
    shape as shape,
    sin as sin,
    sinh as sinh,
    size as size,
    soften_mask as soften_mask,
    sometrue as sometrue,
    sort as sort,
    sqrt as sqrt,
    squeeze as squeeze,
    std as std,
    subtract as subtract,
    sum as sum,
    swapaxes as swapaxes,
    take as take,
    tan as tan,
    tanh as tanh,
    trace as trace,
    transpose as transpose,
    true_divide as true_divide,
    var as var,
    where as where,
    zeros as zeros,
)

from numpy.ma.extras import (
    apply_along_axis as apply_along_axis,
    apply_over_axes as apply_over_axes,
    atleast_1d as atleast_1d,
    atleast_2d as atleast_2d,
    atleast_3d as atleast_3d,
    average as average,
    clump_masked as clump_masked,
    clump_unmasked as clump_unmasked,
    column_stack as column_stack,
    compress_cols as compress_cols,
    compress_nd as compress_nd,
    compress_rowcols as compress_rowcols,
    compress_rows as compress_rows,
    count_masked as count_masked,
    corrcoef as corrcoef,
    cov as cov,
    diagflat as diagflat,
    dot as dot,
    dstack as dstack,
    ediff1d as ediff1d,
    flatnotmasked_contiguous as flatnotmasked_contiguous,
    flatnotmasked_edges as flatnotmasked_edges,
    hsplit as hsplit,
    hstack as hstack,
    isin as isin,
    in1d as in1d,
    intersect1d as intersect1d,
    mask_cols as mask_cols,
    mask_rowcols as mask_rowcols,
    mask_rows as mask_rows,
    masked_all as masked_all,
    masked_all_like as masked_all_like,
    median as median,
    mr_ as mr_,
    ndenumerate as ndenumerate,
    notmasked_contiguous as notmasked_contiguous,
    notmasked_edges as notmasked_edges,
    polyfit as polyfit,
    row_stack as row_stack,
    setdiff1d as setdiff1d,
    setxor1d as setxor1d,
    stack as stack,
    unique as unique,
    union1d as union1d,
    vander as vander,
    vstack as vstack,
)

__all__: list[str]
test: PytestTester
