
# This file was generated by 'versioneer.py' (0.28) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-01-30T18:00:49-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "341209eebf905ad10d3050b67ab495129963dae8",
 "version": "2.0.7"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
