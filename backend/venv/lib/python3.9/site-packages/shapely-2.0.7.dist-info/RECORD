../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/_enum.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/_geometry.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/_ragged_array.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/_version.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/affinity.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/algorithms/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/algorithms/_oriented_envelope.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/algorithms/cga.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/algorithms/polylabel.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/constructive.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/coordinates.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/coords.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/creation.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/decorators.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/errors.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/base.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/collection.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/conftest.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/geo.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/linestring.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/multilinestring.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/multipoint.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/multipolygon.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/point.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geometry/polygon.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/geos.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/io.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/linear.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/measurement.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/ops.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/plotting.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/predicates.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/prepared.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/set_operations.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/speedups.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/strtree.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/testing.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/common.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_collection.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_coords.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_decimal.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_emptiness.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_equality.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_format.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_geometry_base.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_hash.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_linestring.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_multi.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_multilinestring.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_multipoint.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_multipolygon.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_point.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/geometry/test_polygon.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/conftest.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_affinity.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_box.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_buffer.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_cga.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_clip_by_rect.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_create_inconsistent_dimensionality.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_delaunay.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_empty_polygons.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_equality.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_geointerface.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_invalid_geometries.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_linear_referencing.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_linemerge.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_locale.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_make_valid.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_mapping.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_minimum_clearance.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_ndarrays.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_nearest.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_operations.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_operators.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_orient.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_parallel_offset.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_persist.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_pickle.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_polygonize.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_polylabel.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_predicates.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_prepared.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_products_z.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_shape.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_shared_paths.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_singularity.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_snap.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_split.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_substring.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_svg.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_transform.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_union.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_validation.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_vectorized.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_voronoi_diagram.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_wkb.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/test_wkt.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/legacy/threading_test.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_constructive.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_coordinates.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_creation.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_creation_indices.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_geometry.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_io.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_linear.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_measurement.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_misc.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_plotting.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_predicates.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_ragged_array.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_set_operations.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_strtree.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/tests/test_testing.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/validation.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/vectorized/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/wkb.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/shapely/wkt.cpython-39.pyc,,
shapely-2.0.7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
shapely-2.0.7.dist-info/LICENSE.txt,sha256=SiB-rJvyV-0obhxvD84lPRq_2xNPxh7IVeN_VpKj91Q,1583
shapely-2.0.7.dist-info/LICENSE_GEOS,sha256=xwCXD97yCq7HX4C5EeIE1L9W52-LFEbnjSPGkbto9Ok,26795
shapely-2.0.7.dist-info/METADATA,sha256=BQs6-Bl__jeAuaWqAQW2cbZfT1hhah7eJlGwW7kXPFA,6848
shapely-2.0.7.dist-info/RECORD,,
shapely-2.0.7.dist-info/WHEEL,sha256=md3JO_ifs5j508p3TDNMgtQVtnQblpGEt_Wo4W56l8Y,107
shapely-2.0.7.dist-info/top_level.txt,sha256=fxc5UIKKldpKP3lx2dvM5R3hWeKwlmVW6-nfikr3iU0,8
shapely/.dylibs/libgeos.3.11.4.dylib,sha256=A400v1lgN5a-fC4f62YHCBFr_NoD45AirxWRcdBxHHI,2520560
shapely/.dylibs/libgeos_c.1.17.4.dylib,sha256=gVvqEEsIKuxWbAQu-V1eDJFbsDNJBSlsjTYLGzbYCBo,313840
shapely/__init__.py,sha256=JYPRTL-BInpJIzaS2JSpUB2X-O8FAQxYEWJIhYQ5yRY,1049
shapely/_enum.py,sha256=SLuvOs440tV8yDRGbiR8l0AE2WLms1Ri8Z666hDZEOU,713
shapely/_geometry.py,sha256=nCnKeQnsXPLAqOBKnOFjhwYbV4MabIAbB-IqnuWEwsU,24392
shapely/_geometry_helpers.cpython-39-darwin.so,sha256=apCyJbDMuI5c9Hf-Sv48Iuq7nC2Z_i1AEzK1VSKThi8,417696
shapely/_geos.cpython-39-darwin.so,sha256=AP4JWkFY11_Dfb0SKqg0SfsI_663DDJFnfqUCEwp8Qg,135984
shapely/_geos.pxd,sha256=zj448O8W73mHcjjOa3N5NAHOz5ujtdq-f9zwaF9Vxzw,2889
shapely/_pygeos_api.pxd,sha256=jP7i6JBN2HpOn-bybe-2HNBshQq4qmxOmS04T0yA3bo,1518
shapely/_ragged_array.py,sha256=QlAl7SIToSLzeAOM_pAm3BOBg78ajBTnyu02anE7bfU,16570
shapely/_version.py,sha256=xikxkvM6MYdYvbCUimc8JXCh3VsVgMeDxNUferQid30,497
shapely/affinity.py,sha256=v9S2VgSz2zFj44Ne87EKfFuV_pfzo7lFB0YeJybxxf4,8263
shapely/algorithms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/algorithms/_oriented_envelope.py,sha256=rETyzVRYKaGqPeY6xils_QUp22vwjrpfB3uElMS3NFo,1975
shapely/algorithms/cga.py,sha256=GHQBLFI1Epq-Zj92RO3K8yYRado1xNBsrS4djawo068,625
shapely/algorithms/polylabel.py,sha256=vH904OcVSlV-UKIsctp54VBkUBy8_-NuMiyLvRNPclc,4692
shapely/constructive.py,sha256=xb9dNzDsJVyzGEigAwRVV-fvTpZrdMdrQNVOOz4gzRQ,38156
shapely/coordinates.py,sha256=vU6HRO9tuSuX60WwLPTv9ey2qC5LkIJ1_AArWROSnqc,7220
shapely/coords.py,sha256=Mj9Koh0oKMlezWRiZIv2NX2422xM5qiXCsqQSgC5oBE,1779
shapely/creation.py,sha256=AzjVAcaWQwNdJWjPgig7i7QuOBzp-udrNQWeKZagzj8,20282
shapely/decorators.py,sha256=VoRvPWKnEmbHGToGElEfF5f75NPGDagm_kES4N_Cl18,2555
shapely/errors.py,sha256=w77yN5CcXLcy0jZmYBK0YZ_4f0hX-A7qqRoGRKks1Xc,2455
shapely/geometry/__init__.py,sha256=Nlqw8usKbsZUt-PRO02a_zRoyZifJYvUzbUnQrVnaKo,763
shapely/geometry/base.py,sha256=Z4Dorx8Xd1voSoFpuuFvfsy5V0lVXDD2M5RxT_zfipU,34205
shapely/geometry/collection.py,sha256=tOMe2tLl_w3Xl3pHXmMiTEczY9BCltLQYmfkOdBj_r4,1599
shapely/geometry/conftest.py,sha256=QkHIGaq9zHRWmHu1Z5Wu1AMJHSKQZCP7U73rADWXkTM,224
shapely/geometry/geo.py,sha256=qQtXCdGpzif-NOu7KT7bN1guP8ceYEjwmhahB5bTOvk,4098
shapely/geometry/linestring.py,sha256=Wl1yMKmSL0ziAX-QJgybl2ekg_1o1sYCDOlI7ZsC7ck,6786
shapely/geometry/multilinestring.py,sha256=uI6CP6ds_efY9Dy_a6QLkyiZNAIiMdNpJ2Y7IZJ5hVo,2796
shapely/geometry/multipoint.py,sha256=7Jrxb69a5zlAEFmyUfmCRiIxED8T8ghyhKhLsSZNjRM,2683
shapely/geometry/multipolygon.py,sha256=9LS73gtyOKs22N8Btz4WzGxFlljy1obtOgb0u80lhos,3843
shapely/geometry/point.py,sha256=mfVbttP_z91X6dq2QRwj_sQJPHt1LWITiRtk-eMIYBY,4197
shapely/geometry/polygon.py,sha256=jrBDmu7ZM1KzccVShZxehS_CIk5xeWSNgeX9LvR17DI,11376
shapely/geos.py,sha256=xL5Tejiyjw1t7y51pZBNtcrxYZn4m8G1q39qthxi2Rk,222
shapely/io.py,sha256=EDpHDMgaS7P99Uib4qHFv0tC_fYbKg_BJbKxPfgpOGc,13345
shapely/lib.cpython-39-darwin.so,sha256=ED7C5fxMzddSTJSX29EwLeZZw2Q6AFQXS6QPK3_suq8,250848
shapely/linear.py,sha256=C_us0k6iJ9RC2HWR5oBQRCDU7Xzm219Hexd5CcDXf38,7297
shapely/measurement.py,sha256=W10XJnoPG9msghnoA3QS-qxiCP40u6wS-UvhhG8lQEg,9727
shapely/ops.py,sha256=1-OuJRfoSM0tuO0blDeCkuszwOYvli6hlpKEVDJ3ff4,26182
shapely/plotting.py,sha256=0zc8oGpROomRSCQTnYmTIaeKl5_yX_QoTFb2OqssrbQ,6160
shapely/predicates.py,sha256=7yGLn0oG0IXg_bGltdunseXijBi2IveO5WF04z0lK8k,32952
shapely/prepared.py,sha256=kcE28dEU3RayHycEH6NNp02nGq6tVPRK2KMsErCL8Fk,2402
shapely/set_operations.py,sha256=uEXzdhkbWJacpalPtdX0iEqz6XPcJYtvX8MJ5sHAWMY,17553
shapely/speedups.py,sha256=V7olKLqaE_F5iJ-cSMWfymQFrLsKj4zUx5u_qGzUvxU,948
shapely/strtree.py,sha256=m7gTvKZ03a6eZ5UNw3QUqEVWGIBLZmLofBtTFccyZvk,20390
shapely/testing.py,sha256=o5pTV_kcBha0xDi8w_UvFkdSNDkr2e8Nk73jyZ9KYn8,6313
shapely/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/common.py,sha256=YR84dak4frQH_XDDAfSwKQrTxuX5Mm9FBFC0VZcly5A,3592
shapely/tests/geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/geometry/test_collection.py,sha256=j3SPQwuw-L14UvtR3-ZFD7VDckesPPoL7eqRNZcZ5II,2230
shapely/tests/geometry/test_coords.py,sha256=iaMhw3oh--yEgtxgNhWwsT0nC9JxcW-WS_oCuKUroP4,3436
shapely/tests/geometry/test_decimal.py,sha256=xGPoBZ7kR6dzoXQeVufZv3pA-DE9sA3O4TbuFPsVWdg,2750
shapely/tests/geometry/test_emptiness.py,sha256=FIPHsZWLgRoehKpvoezAsqjjzJnosL7y08a-AbHlbA4,2362
shapely/tests/geometry/test_equality.py,sha256=KiwjrFOhueS7-emfTgEfPj0m2BtBX-8FQT7UolgGRGQ,7982
shapely/tests/geometry/test_format.py,sha256=a3RyDuGg-Tzc08gzKgiFPHTvU7FC580YLrmC88OYN0s,4557
shapely/tests/geometry/test_geometry_base.py,sha256=iwePTN1v2Z5NMIrjihQWBoyZAoCsRnjB__ytIVn9RPw,7971
shapely/tests/geometry/test_hash.py,sha256=EtQyaRUtTUIL8AiCjyvVC4KRJQ_E3uqr-4Wn_oaW-Ac,673
shapely/tests/geometry/test_linestring.py,sha256=e_81lvGmqWm7GT_0OvzDcoldoBPju-7g-01zbUpSGBE,6339
shapely/tests/geometry/test_multi.py,sha256=SejfKyNSH27bxQnLwouRHnxgrlQV6egaxLADR4G9ZBY,304
shapely/tests/geometry/test_multilinestring.py,sha256=yFW5ctDIVvzrsPm8v9tnQByho6fs3ngR8nV31l6F1EU,2835
shapely/tests/geometry/test_multipoint.py,sha256=y0ewZ0y85HayrjpLPfsrhAXySPz-rcC2KuGrOro2dTg,2435
shapely/tests/geometry/test_multipolygon.py,sha256=WTNTrkEUNhgokvYuIA6oblF1wkAnvYskS5417865AIs,4074
shapely/tests/geometry/test_point.py,sha256=k-faVhiIIRJvzZ4etXtoXUeKYYgPRAx33gtdt1ziLoM,4778
shapely/tests/geometry/test_polygon.py,sha256=9__zIzfXrqOBkgAmpR_FHa1uMkTP9FDfv4I3yrjXw1k,15293
shapely/tests/legacy/__init__.py,sha256=Jhb3gRBggyQeAgQVpobtcJAuqaVlFptXkTR9t3z48Ik,275
shapely/tests/legacy/conftest.py,sha256=dve1hOGWMjaOk58k_RUbU1RvnLCPLopnwGvUSMAmqrk,588
shapely/tests/legacy/test_affinity.py,sha256=Q03K9atdQa5r-c4fVxvmuQDbAb1B4-EGZb6-Sx4SPtY,12531
shapely/tests/legacy/test_box.py,sha256=dWUFGKq_ItxQNw613IPqkAJu0vAbCKDSiXNtXfaU7yI,599
shapely/tests/legacy/test_buffer.py,sha256=LqeEwKb4irvmorGOYwbSqhB3hHFF6Ag2bnfZoULyTlg,6521
shapely/tests/legacy/test_cga.py,sha256=FmLoEAfyWO9fvdrYLuxiDn4pk7fi60RN14eMBv1VXU8,1509
shapely/tests/legacy/test_clip_by_rect.py,sha256=zBID16XD31PYB0Ojls9x-8UTzhjBEL-9CqFYSszz5Vg,4180
shapely/tests/legacy/test_create_inconsistent_dimensionality.py,sha256=RXTo4yMWv15pWEyERPYdJhZ-nCqyz1WiTMOxZ2Q40Fo,10644
shapely/tests/legacy/test_delaunay.py,sha256=TubjNzbTxij9N9VDRX9Po_3MpxytonkCoAExq5aVaRs,838
shapely/tests/legacy/test_empty_polygons.py,sha256=pNjl0EVWuphek4TPHHqydPq10JqImQC0y963erL5K7E,683
shapely/tests/legacy/test_equality.py,sha256=Q0lUt_1MbXrzUwnLSW6w4U1ThRvYNaUkO6NgdTxL0xI,931
shapely/tests/legacy/test_geointerface.py,sha256=dAsuOZSaVTZjWXquLlC0bA7MufQ8-Rr67J7FWQCfj4o,3648
shapely/tests/legacy/test_invalid_geometries.py,sha256=K-lRgzKTXcjRoXhfaNOILD6DzR2DO3XJfsNXjbrlpfs,907
shapely/tests/legacy/test_linear_referencing.py,sha256=CBHU1SXOHor7WKGww1ZmoSgLqa0QsuhfVPK21aiNv0Y,2914
shapely/tests/legacy/test_linemerge.py,sha256=AfoJ-20T6t3aw6EBCAG8VAYBJqWjDdaq6m3laxLAxlM,1330
shapely/tests/legacy/test_locale.py,sha256=zlaz6tM71Do_2xyCbfzQEMREzfbFQYzscSrvKl1dEqc,1436
shapely/tests/legacy/test_make_valid.py,sha256=6s-sf6RrcVUxwAFcG1fkmBnCtv4wHQXVD6FuA61YrD4,574
shapely/tests/legacy/test_mapping.py,sha256=5CiH3Ab0BMkxbluE5T1A0lDYXWqbfpEESLkFcIKmr6I,395
shapely/tests/legacy/test_minimum_clearance.py,sha256=jaV8heru7YwU1xWo8QO30qIcE4wR7B8tLz8DMtdZnfQ,916
shapely/tests/legacy/test_ndarrays.py,sha256=5_sHeWOJ49rt41pLl5j41IbWFDDcSW0kAFqtC45jKkU,1244
shapely/tests/legacy/test_nearest.py,sha256=eXHfHQA0k1cUYNcn9WvL9SpnwhCjFb2CeN2NAMHQ0M0,476
shapely/tests/legacy/test_operations.py,sha256=0OpFGJW7bMvs77ar_OZ8L4SZlrZ0Kdqa_sTC6HtWTXs,4267
shapely/tests/legacy/test_operators.py,sha256=aWjGgHOmLm6Jx21BiR9APBFokL4hxOrC1IRY8YHo0QU,1948
shapely/tests/legacy/test_orient.py,sha256=dVmRd2O2j_bO4UOjVq1lr6prijUTeIWXtDUVR4gZ_BA,2400
shapely/tests/legacy/test_parallel_offset.py,sha256=GRiJa48w2-z7NXDVgjyh5wPTCt_lQZg5l_b1bExDZ4U,2332
shapely/tests/legacy/test_persist.py,sha256=SrwIiAOOQ1PrmXEeOO4t3dOswkSQhXvcdGnlM4yyvkI,1713
shapely/tests/legacy/test_pickle.py,sha256=LvyCTWS3hkwG7hM-Y71iQ9KEcP9jfoPA2ji3QYN64h0,2481
shapely/tests/legacy/test_polygonize.py,sha256=m9euuuzwqS7mfT-nxtfoheIzrvoSXlhMaL0phXo_HZk,1389
shapely/tests/legacy/test_polylabel.py,sha256=GHgr9HcJ9kLHZIiv1UE99ut3TfjjRhr7MrCNVsnMjlQ,3225
shapely/tests/legacy/test_predicates.py,sha256=EVE29XaKVrZni9TxkkohEdQJHQDuSf_Y0RwT4FGBhNo,2962
shapely/tests/legacy/test_prepared.py,sha256=NbuyzVwY3taqrnn6swX4HIfSQtV8rpFO__dpwZLhoUQ,2347
shapely/tests/legacy/test_products_z.py,sha256=CrN95z-8sl3J76YZE05E5tYvMLrhGDTp_Rivbblbq5g,388
shapely/tests/legacy/test_shape.py,sha256=3F3YHrtDBcl-BtjdMhbXGNaFs5KrCL5r4nB_kZoYilE,1404
shapely/tests/legacy/test_shared_paths.py,sha256=KZ1FM6s2O315voIw1XOEluwMnfzhyLd91yXnpNmnt7Y,1434
shapely/tests/legacy/test_singularity.py,sha256=GI-_673c18SIAq1UQZT3ZOnfeSbhBEuGAYgCuzPldho,381
shapely/tests/legacy/test_snap.py,sha256=4KkXVxpZV38wTgxYZuGTVou1F9Co7qCPMixGouMZUqI,764
shapely/tests/legacy/test_split.py,sha256=YgGUaiPtbq1SFSj5nGcj_gp1l3wYbRsXxSIdk0D_g3s,9714
shapely/tests/legacy/test_substring.py,sha256=E-Rnm0tOqjUnRZtSEgCUPyknYlnraYIWpQaozeAT0OQ,21148
shapely/tests/legacy/test_svg.py,sha256=xIXhkL44w1PJl3suxvDjXKd9nPO7ljkYMTz3qoD7DG0,8253
shapely/tests/legacy/test_transform.py,sha256=s5GWsu9EeW__D0QHT4L9ivr1lygeiSxRNLEyGgmdksk,2705
shapely/tests/legacy/test_union.py,sha256=LHatZT6dgMhQLEZJ4pOUaDSoD0UqPnbjAefHbWsvxkU,2319
shapely/tests/legacy/test_validation.py,sha256=L33s2pjag9R_HoDa-mUG1oEOZcK-pexhnQHX4xlZu6I,238
shapely/tests/legacy/test_vectorized.py,sha256=I5haar26ma34Cs7WgQPRuxmTnEM2Di8HlALy5S-xh6o,3583
shapely/tests/legacy/test_voronoi_diagram.py,sha256=ceWf2YmgUhwwLWfZWZxbVSftF2u3RYwD-fiPeAIcgts,4444
shapely/tests/legacy/test_wkb.py,sha256=iEHmD47yLbdnoetgMhzy-K202FTpAwO5uB64NEJWOf4,6121
shapely/tests/legacy/test_wkt.py,sha256=2RCgxa5SH7-kVotkEy1_WBb8eiN8S1Ji-dwdflsCMng,1610
shapely/tests/legacy/threading_test.py,sha256=ZVGj2PC-wEZ0Pr2VJgYPAM2iqd5LOs3Bepj6qWK_sz8,1032
shapely/tests/test_constructive.py,sha256=UuxFFAiccRa2NuFrkfOL_IydR7nHv_9ac8FBud2Y0vA,33026
shapely/tests/test_coordinates.py,sha256=aQXGOa-HWT-Un8RyWzzkJz62IsSqMwE63J4DxrwsAH0,9068
shapely/tests/test_creation.py,sha256=ubGQEp9o-tbpflqlmr7PLkYUlU_2DapPHusGG1abYYE,17868
shapely/tests/test_creation_indices.py,sha256=9t1T5SSoaiPHJ3u69iCdPMKVWgVmSA44yeICREIGlQs,13398
shapely/tests/test_geometry.py,sha256=c68KhRcv-jmT9Pkxq-4Iw5yME0LVmyF8RcEPz87tkZw,24454
shapely/tests/test_io.py,sha256=WOOz_Nc4B_KpmFwM8Ci16moKlKbQ53Bc1q2lSzdgimg,29657
shapely/tests/test_linear.py,sha256=HQzdqiXGni70LdmwDoEqbrXSYxGVD9mm8dDzD00EpDQ,7441
shapely/tests/test_measurement.py,sha256=ptv26_VDRgQTb9Z-Bi0GonnsvQpVTPfnUieSVUhZoRY,10773
shapely/tests/test_misc.py,sha256=DMSIqnKr9LY6qeF8CSRd4TxtLc-RLLE164NvawvzwxI,5602
shapely/tests/test_plotting.py,sha256=jQX209kK6Nbs5YyF4kdpCJXDzPXqWnUznKXKQBd3o2A,3615
shapely/tests/test_predicates.py,sha256=ywpuGM9ffUND51ZDM5E73T0wy_mIkI87ro2U9X0lu18,10981
shapely/tests/test_ragged_array.py,sha256=f2tDJXJs438g0nTCueNoxwKN5Y6-SL4k4Gv4QmXyM7g,11953
shapely/tests/test_set_operations.py,sha256=k8wQh3v0__XOyR7H3lUJ9joo7rlxuXMSlUvEnIfnv00,16932
shapely/tests/test_strtree.py,sha256=GHdoucXBQIKe4vKcy_-5LRS3LUd1eXEg5Xet14xFpI4,74400
shapely/tests/test_testing.py,sha256=fLGSiETvPPrvdDRvqxZUx63s3HohJG0d0FaOmsyMJR4,3072
shapely/validation.py,sha256=gRJpmvX2JLELktQT_qr6O2tlcrCf7SJNy0bgDSIvafA,1433
shapely/vectorized/__init__.py,sha256=j4d9L0I23M84KXkHuDqKhQCEYsgY7DjtPznJuTbFVs8,2249
shapely/wkb.py,sha256=BuuJm9NDUiLtPWMu1pl8dVpx4AN0MW6cCyN1hvyDrPA,1912
shapely/wkt.py,sha256=kDPrPUtQnTrfsEdLi7APqBAkIoqcUSccrK1mmkWXOks,1947
