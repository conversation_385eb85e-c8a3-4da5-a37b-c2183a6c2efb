"""
数据库模型定义
"""

from sqlalchemy import Column, Integer, String, Text, Float, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()


class OCRTask(Base):
    """OCR任务表"""
    __tablename__ = "ocr_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(36), unique=True, index=True, nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(20), nullable=False, default="pending")
    priority = Column(Integer, default=2)  # 1=low, 2=normal, 3=high, 4=urgent
    
    # 配置信息
    language = Column(String(10), default="ch")
    mode = Column(String(20), default="accurate")
    output_format = Column(String(10), default="txt")
    use_angle_cls = Column(Boolean, default=True)
    use_space_char = Column(Boolean, default=True)
    
    # 统计信息
    total_files = Column(Integer, default=0)
    processed_files = Column(Integer, default=0)
    successful_files = Column(Integer, default=0)
    failed_files = Column(Integer, default=0)
    progress = Column(Float, default=0.0)
    
    # 时间信息
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # 错误信息
    error_message = Column(Text)
    
    # 关联关系
    files = relationship("OCRFile", back_populates="task", cascade="all, delete-orphan")
    results = relationship("OCRResult", back_populates="task", cascade="all, delete-orphan")


class OCRFile(Base):
    """OCR文件表"""
    __tablename__ = "ocr_files"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(36), ForeignKey("ocr_tasks.task_id"), nullable=False)
    
    # 文件信息
    original_filename = Column(String(255), nullable=False)
    stored_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    file_hash = Column(String(32))
    mime_type = Column(String(100))
    extension = Column(String(10))
    
    # 处理状态
    status = Column(String(20), default="pending")  # pending, processing, completed, failed
    processed_at = Column(DateTime)
    
    # 时间信息
    uploaded_at = Column(DateTime, default=func.now())
    
    # 关联关系
    task = relationship("OCRTask", back_populates="files")
    result = relationship("OCRResult", back_populates="file", uselist=False)


class OCRResult(Base):
    """OCR识别结果表"""
    __tablename__ = "ocr_results"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(36), ForeignKey("ocr_tasks.task_id"), nullable=False)
    file_id = Column(Integer, ForeignKey("ocr_files.id"), nullable=False)
    
    # 识别结果
    text_content = Column(Text)
    confidence = Column(Float, default=0.0)
    word_count = Column(Integer, default=0)
    line_count = Column(Integer, default=0)
    
    # 详细结果（JSON格式存储边界框等信息）
    details = Column(JSON)
    
    # 处理信息
    processing_time = Column(Float, default=0.0)
    success = Column(Boolean, default=False)
    error_message = Column(Text)
    
    # 编辑信息
    is_edited = Column(Boolean, default=False)
    original_text = Column(Text)  # 保存原始识别文本
    edited_at = Column(DateTime)
    
    # 时间信息
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    task = relationship("OCRTask", back_populates="results")
    file = relationship("OCRFile", back_populates="result")


class ExportRecord(Base):
    """导出记录表"""
    __tablename__ = "export_records"
    
    id = Column(Integer, primary_key=True, index=True)
    export_id = Column(String(36), unique=True, index=True, nullable=False)
    
    # 导出信息
    export_type = Column(String(20), nullable=False)  # single, batch, task
    format = Column(String(10), nullable=False)  # txt, docx, pdf, json
    
    # 关联信息
    task_ids = Column(JSON)  # 导出的任务ID列表
    result_ids = Column(JSON)  # 导出的结果ID列表
    
    # 文件信息
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    
    # 统计信息
    total_results = Column(Integer, default=0)
    total_text_length = Column(Integer, default=0)
    
    # 时间信息
    created_at = Column(DateTime, default=func.now())
    expires_at = Column(DateTime)  # 文件过期时间


class SystemLog(Base):
    """系统日志表"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 日志信息
    level = Column(String(10), nullable=False)  # DEBUG, INFO, WARNING, ERROR
    message = Column(Text, nullable=False)
    module = Column(String(50))
    function = Column(String(50))
    
    # 关联信息
    task_id = Column(String(36))
    user_id = Column(String(36))
    
    # 额外信息
    extra_data = Column(JSON)
    
    # 时间信息
    created_at = Column(DateTime, default=func.now())


class SystemStats(Base):
    """系统统计表"""
    __tablename__ = "system_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 统计日期
    date = Column(DateTime, nullable=False, index=True)
    
    # 任务统计
    total_tasks = Column(Integer, default=0)
    completed_tasks = Column(Integer, default=0)
    failed_tasks = Column(Integer, default=0)
    
    # 文件统计
    total_files = Column(Integer, default=0)
    total_file_size = Column(Integer, default=0)
    
    # 识别统计
    total_text_length = Column(Integer, default=0)
    average_confidence = Column(Float, default=0.0)
    
    # 性能统计
    average_processing_time = Column(Float, default=0.0)
    
    # 时间信息
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
