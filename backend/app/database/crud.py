"""
数据库CRUD操作
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_
from datetime import datetime, timedelta

from app.database.models import OCRTask, OCRFile, OCRResult, ExportRecord, SystemLog, SystemStats
from app.core.task_manager import TaskStatus, TaskPriority


class TaskCRUD:
    """任务相关的CRUD操作"""
    
    @staticmethod
    def create_task(db: Session, task_data: Dict[str, Any]) -> OCRTask:
        """创建任务"""
        db_task = OCRTask(**task_data)
        db.add(db_task)
        db.commit()
        db.refresh(db_task)
        return db_task
    
    @staticmethod
    def get_task(db: Session, task_id: str) -> Optional[OCRTask]:
        """获取单个任务"""
        return db.query(OCRTask).filter(OCRTask.task_id == task_id).first()
    
    @staticmethod
    def get_tasks(db: Session, skip: int = 0, limit: int = 100, 
                  status: Optional[str] = None) -> List[OCRTask]:
        """获取任务列表"""
        query = db.query(OCRTask)
        
        if status:
            query = query.filter(OCRTask.status == status)
        
        return query.order_by(desc(OCRTask.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def update_task(db: Session, task_id: str, update_data: Dict[str, Any]) -> Optional[OCRTask]:
        """更新任务"""
        db_task = db.query(OCRTask).filter(OCRTask.task_id == task_id).first()
        if db_task:
            for key, value in update_data.items():
                setattr(db_task, key, value)
            db.commit()
            db.refresh(db_task)
        return db_task
    
    @staticmethod
    def delete_task(db: Session, task_id: str) -> bool:
        """删除任务"""
        db_task = db.query(OCRTask).filter(OCRTask.task_id == task_id).first()
        if db_task:
            db.delete(db_task)
            db.commit()
            return True
        return False
    
    @staticmethod
    def get_task_statistics(db: Session) -> Dict[str, Any]:
        """获取任务统计信息"""
        total = db.query(OCRTask).count()
        pending = db.query(OCRTask).filter(OCRTask.status == "pending").count()
        processing = db.query(OCRTask).filter(OCRTask.status == "processing").count()
        completed = db.query(OCRTask).filter(OCRTask.status == "completed").count()
        failed = db.query(OCRTask).filter(OCRTask.status == "failed").count()
        
        return {
            "total": total,
            "pending": pending,
            "processing": processing,
            "completed": completed,
            "failed": failed
        }


class FileCRUD:
    """文件相关的CRUD操作"""
    
    @staticmethod
    def create_file(db: Session, file_data: Dict[str, Any]) -> OCRFile:
        """创建文件记录"""
        db_file = OCRFile(**file_data)
        db.add(db_file)
        db.commit()
        db.refresh(db_file)
        return db_file
    
    @staticmethod
    def get_file(db: Session, file_id: int) -> Optional[OCRFile]:
        """获取单个文件"""
        return db.query(OCRFile).filter(OCRFile.id == file_id).first()
    
    @staticmethod
    def get_files_by_task(db: Session, task_id: str) -> List[OCRFile]:
        """获取任务的所有文件"""
        return db.query(OCRFile).filter(OCRFile.task_id == task_id).all()
    
    @staticmethod
    def update_file(db: Session, file_id: int, update_data: Dict[str, Any]) -> Optional[OCRFile]:
        """更新文件"""
        db_file = db.query(OCRFile).filter(OCRFile.id == file_id).first()
        if db_file:
            for key, value in update_data.items():
                setattr(db_file, key, value)
            db.commit()
            db.refresh(db_file)
        return db_file
    
    @staticmethod
    def delete_file(db: Session, file_id: int) -> bool:
        """删除文件"""
        db_file = db.query(OCRFile).filter(OCRFile.id == file_id).first()
        if db_file:
            db.delete(db_file)
            db.commit()
            return True
        return False


class ResultCRUD:
    """结果相关的CRUD操作"""
    
    @staticmethod
    def create_result(db: Session, result_data: Dict[str, Any]) -> OCRResult:
        """创建识别结果"""
        db_result = OCRResult(**result_data)
        db.add(db_result)
        db.commit()
        db.refresh(db_result)
        return db_result
    
    @staticmethod
    def get_result(db: Session, result_id: int) -> Optional[OCRResult]:
        """获取单个结果"""
        return db.query(OCRResult).filter(OCRResult.id == result_id).first()
    
    @staticmethod
    def get_results_by_task(db: Session, task_id: str) -> List[OCRResult]:
        """获取任务的所有结果"""
        return db.query(OCRResult).filter(OCRResult.task_id == task_id).all()
    
    @staticmethod
    def update_result(db: Session, result_id: int, update_data: Dict[str, Any]) -> Optional[OCRResult]:
        """更新结果"""
        db_result = db.query(OCRResult).filter(OCRResult.id == result_id).first()
        if db_result:
            # 如果更新文本内容，标记为已编辑
            if 'text_content' in update_data and db_result.text_content != update_data['text_content']:
                if not db_result.is_edited:
                    db_result.original_text = db_result.text_content
                    db_result.is_edited = True
                    db_result.edited_at = datetime.now()
            
            for key, value in update_data.items():
                setattr(db_result, key, value)
            
            db.commit()
            db.refresh(db_result)
        return db_result
    
    @staticmethod
    def search_results(db: Session, query: str, skip: int = 0, limit: int = 100) -> List[OCRResult]:
        """搜索结果"""
        return (db.query(OCRResult)
                .filter(OCRResult.text_content.contains(query))
                .order_by(desc(OCRResult.created_at))
                .offset(skip)
                .limit(limit)
                .all())
    
    @staticmethod
    def get_results_by_date_range(db: Session, start_date: datetime, 
                                end_date: datetime) -> List[OCRResult]:
        """根据日期范围获取结果"""
        return (db.query(OCRResult)
                .filter(and_(OCRResult.created_at >= start_date,
                           OCRResult.created_at <= end_date))
                .order_by(desc(OCRResult.created_at))
                .all())


class ExportCRUD:
    """导出相关的CRUD操作"""
    
    @staticmethod
    def create_export_record(db: Session, export_data: Dict[str, Any]) -> ExportRecord:
        """创建导出记录"""
        db_export = ExportRecord(**export_data)
        db.add(db_export)
        db.commit()
        db.refresh(db_export)
        return db_export
    
    @staticmethod
    def get_export_record(db: Session, export_id: str) -> Optional[ExportRecord]:
        """获取导出记录"""
        return db.query(ExportRecord).filter(ExportRecord.export_id == export_id).first()
    
    @staticmethod
    def get_export_records(db: Session, skip: int = 0, limit: int = 100) -> List[ExportRecord]:
        """获取导出记录列表"""
        return (db.query(ExportRecord)
                .order_by(desc(ExportRecord.created_at))
                .offset(skip)
                .limit(limit)
                .all())
    
    @staticmethod
    def delete_expired_exports(db: Session) -> int:
        """删除过期的导出记录"""
        now = datetime.now()
        expired_records = (db.query(ExportRecord)
                         .filter(ExportRecord.expires_at < now)
                         .all())
        
        count = len(expired_records)
        for record in expired_records:
            db.delete(record)
        
        db.commit()
        return count


class LogCRUD:
    """日志相关的CRUD操作"""
    
    @staticmethod
    def create_log(db: Session, log_data: Dict[str, Any]) -> SystemLog:
        """创建日志"""
        db_log = SystemLog(**log_data)
        db.add(db_log)
        db.commit()
        db.refresh(db_log)
        return db_log
    
    @staticmethod
    def get_logs(db: Session, level: Optional[str] = None, 
                module: Optional[str] = None, skip: int = 0, limit: int = 100) -> List[SystemLog]:
        """获取日志列表"""
        query = db.query(SystemLog)
        
        if level:
            query = query.filter(SystemLog.level == level)
        
        if module:
            query = query.filter(SystemLog.module == module)
        
        return query.order_by(desc(SystemLog.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def cleanup_old_logs(db: Session, days: int = 30) -> int:
        """清理旧日志"""
        cutoff_date = datetime.now() - timedelta(days=days)
        old_logs = db.query(SystemLog).filter(SystemLog.created_at < cutoff_date).all()
        
        count = len(old_logs)
        for log in old_logs:
            db.delete(log)
        
        db.commit()
        return count


class StatsCRUD:
    """统计相关的CRUD操作"""
    
    @staticmethod
    def create_or_update_stats(db: Session, date: datetime, stats_data: Dict[str, Any]) -> SystemStats:
        """创建或更新统计数据"""
        # 查找当天的统计记录
        db_stats = (db.query(SystemStats)
                   .filter(SystemStats.date == date.date())
                   .first())
        
        if db_stats:
            # 更新现有记录
            for key, value in stats_data.items():
                setattr(db_stats, key, value)
        else:
            # 创建新记录
            stats_data['date'] = date.date()
            db_stats = SystemStats(**stats_data)
            db.add(db_stats)
        
        db.commit()
        db.refresh(db_stats)
        return db_stats
    
    @staticmethod
    def get_stats_by_date_range(db: Session, start_date: datetime, 
                              end_date: datetime) -> List[SystemStats]:
        """根据日期范围获取统计数据"""
        return (db.query(SystemStats)
                .filter(and_(SystemStats.date >= start_date.date(),
                           SystemStats.date <= end_date.date()))
                .order_by(asc(SystemStats.date))
                .all())
