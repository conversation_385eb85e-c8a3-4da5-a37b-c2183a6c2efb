"""
FastAPI应用主入口
"""

import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger
import sys
from datetime import datetime

from app.config import get_settings
from app.database.database import get_database_manager
from app.core.task_manager import get_task_manager
from app.api.routes import ocr, tasks, results
from app.api.schemas import HealthCheckResponse


# 配置日志
def setup_logging():
    """配置日志系统"""
    settings = get_settings()
    
    # 移除默认处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=settings.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 添加文件输出
    logger.add(
        settings.LOG_FILE,
        level=settings.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days",
        compression="zip"
    )


# 应用启动时间
app_start_time = datetime.now()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("OCR智能识别平台启动中...")
    
    # 初始化数据库
    db_manager = get_database_manager()
    logger.info("数据库初始化完成")
    
    # 初始化任务管理器
    task_manager = get_task_manager()
    
    # 启动任务处理器
    task_processor = asyncio.create_task(task_manager.start_task_processing())
    logger.info("任务处理器启动完成")
    
    logger.info("OCR智能识别平台启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("OCR智能识别平台关闭中...")
    
    # 取消任务处理器
    task_processor.cancel()
    try:
        await task_processor
    except asyncio.CancelledError:
        pass
    
    logger.info("OCR智能识别平台已关闭")


# 创建FastAPI应用
def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description="基于PaddleOCR的智能文字识别服务",
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(ocr.router)
    app.include_router(tasks.router)
    app.include_router(results.router)
    
    # 静态文件服务（用于前端）
    # 暂时注释掉静态文件服务，专注于API测试
    # app.mount("/static", StaticFiles(directory="static"), name="static")
    
    return app


# 设置日志
setup_logging()

# 创建应用实例
app = create_app()


@app.get("/", tags=["Root"])
async def root():
    """根路径"""
    return {
        "message": "OCR智能识别平台API",
        "version": get_settings().APP_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health", response_model=HealthCheckResponse, tags=["Health"])
async def health_check():
    """健康检查"""
    try:
        settings = get_settings()
        
        # 检查数据库连接
        try:
            db_manager = get_database_manager()
            with db_manager.get_session_context() as db:
                from sqlalchemy import text
                db.execute(text("SELECT 1"))
            database_status = "healthy"
        except Exception as e:
            logger.error(f"数据库健康检查失败: {str(e)}")
            database_status = "unhealthy"
        
        # 检查OCR引擎
        try:
            from app.core.ocr_engine import get_ocr_engine
            ocr_engine = get_ocr_engine()
            # 简单检查OCR引擎是否可用
            ocr_engine._get_ocr_instance()
            ocr_engine_status = "healthy"
        except Exception as e:
            logger.error(f"OCR引擎健康检查失败: {str(e)}")
            ocr_engine_status = "unhealthy"
        
        # 计算运行时间
        uptime = (datetime.now() - app_start_time).total_seconds()
        
        # 确定整体状态
        overall_status = "healthy" if database_status == "healthy" and ocr_engine_status == "healthy" else "unhealthy"
        
        return HealthCheckResponse(
            status=overall_status,
            timestamp=datetime.now(),
            version=settings.APP_VERSION,
            uptime=uptime,
            database_status=database_status,
            ocr_engine_status=ocr_engine_status
        )
    
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail="健康检查失败")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "服务器内部错误",
            "detail": str(exc) if get_settings().DEBUG else "请联系管理员"
        }
    )


if __name__ == "__main__":
    settings = get_settings()
    
    logger.info(f"启动OCR智能识别平台服务器...")
    logger.info(f"服务地址: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"API文档: http://{settings.HOST}:{settings.PORT}/docs")
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
