"""
文件处理模块
处理文件上传、格式转换、图片预处理等功能
"""

import os
import shutil
import hashlib
import mimetypes
from typing import List, Dict, Any, Optional, Union, BinaryIO
from pathlib import Path
from datetime import datetime
import aiofiles
from PIL import Image
import magic
from loguru import logger

from app.config import get_settings


class FileHandler:
    """文件处理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.settings.UPLOAD_DIR,
            self.settings.RESULTS_DIR,
            os.path.join(self.settings.UPLOAD_DIR, "temp"),
            os.path.join(self.settings.UPLOAD_DIR, "processed"),
            os.path.join(self.settings.RESULTS_DIR, "exports")
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _generate_file_hash(self, file_content: bytes) -> str:
        """生成文件哈希值"""
        return hashlib.md5(file_content).hexdigest()
    
    def _get_safe_filename(self, filename: str) -> str:
        """生成安全的文件名"""
        # 移除危险字符
        safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_"
        safe_filename = "".join(c for c in filename if c in safe_chars)
        
        # 确保文件名不为空
        if not safe_filename:
            safe_filename = "unnamed_file"
        
        return safe_filename
    
    def _get_unique_filename(self, directory: str, filename: str) -> str:
        """获取唯一的文件名（避免重复）"""
        base_path = Path(directory)
        file_path = base_path / filename
        
        if not file_path.exists():
            return filename
        
        # 如果文件已存在，添加时间戳
        name = Path(filename).stem
        ext = Path(filename).suffix
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return f"{name}_{timestamp}{ext}"
    
    def validate_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """验证文件"""
        try:
            # 检查文件大小
            if len(file_content) > self.settings.MAX_FILE_SIZE:
                return {
                    "valid": False,
                    "error": f"文件大小超过限制 ({self.settings.MAX_FILE_SIZE / 1024 / 1024:.1f}MB)"
                }
            
            # 检查文件扩展名
            file_ext = Path(filename).suffix.lower()
            if file_ext not in self.settings.ALLOWED_EXTENSIONS:
                return {
                    "valid": False,
                    "error": f"不支持的文件格式: {file_ext}"
                }
            
            # 检查文件MIME类型
            mime_type = magic.from_buffer(file_content, mime=True)
            if not mime_type.startswith('image/'):
                return {
                    "valid": False,
                    "error": f"文件类型不正确: {mime_type}"
                }
            
            # 尝试打开图片验证
            try:
                from io import BytesIO
                Image.open(BytesIO(file_content)).verify()
            except Exception:
                return {
                    "valid": False,
                    "error": "图片文件损坏或格式不正确"
                }
            
            return {
                "valid": True,
                "size": len(file_content),
                "mime_type": mime_type,
                "extension": file_ext
            }
            
        except Exception as e:
            logger.error(f"文件验证失败: {filename}, 错误: {str(e)}")
            return {
                "valid": False,
                "error": f"文件验证失败: {str(e)}"
            }
    
    async def save_uploaded_file(self, file_content: bytes, filename: str, 
                               subdirectory: str = "temp") -> Dict[str, Any]:
        """保存上传的文件"""
        try:
            # 验证文件
            validation = self.validate_file(file_content, filename)
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["error"]
                }
            
            # 生成安全的文件名
            safe_filename = self._get_safe_filename(filename)
            
            # 确定保存目录
            save_dir = os.path.join(self.settings.UPLOAD_DIR, subdirectory)
            Path(save_dir).mkdir(parents=True, exist_ok=True)
            
            # 生成唯一文件名
            unique_filename = self._get_unique_filename(save_dir, safe_filename)
            file_path = os.path.join(save_dir, unique_filename)
            
            # 异步保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # 生成文件信息
            file_hash = self._generate_file_hash(file_content)
            
            logger.info(f"文件保存成功: {file_path}")
            
            return {
                "success": True,
                "file_path": file_path,
                "filename": unique_filename,
                "original_filename": filename,
                "size": len(file_content),
                "hash": file_hash,
                "mime_type": validation["mime_type"],
                "extension": validation["extension"]
            }
            
        except Exception as e:
            logger.error(f"文件保存失败: {filename}, 错误: {str(e)}")
            return {
                "success": False,
                "error": f"文件保存失败: {str(e)}"
            }
    
    async def save_multiple_files(self, files: List[Dict[str, Any]], 
                                subdirectory: str = "temp") -> List[Dict[str, Any]]:
        """批量保存文件"""
        results = []
        
        for file_info in files:
            file_content = file_info.get("content")
            filename = file_info.get("filename")
            
            if not file_content or not filename:
                results.append({
                    "success": False,
                    "filename": filename or "unknown",
                    "error": "文件内容或文件名缺失"
                })
                continue
            
            result = await self.save_uploaded_file(file_content, filename, subdirectory)
            results.append(result)
        
        return results
    
    def move_file(self, source_path: str, target_directory: str, 
                  new_filename: Optional[str] = None) -> Dict[str, Any]:
        """移动文件到指定目录"""
        try:
            source_path = Path(source_path)
            target_dir = Path(target_directory)
            
            # 确保目标目录存在
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 确定目标文件名
            if new_filename:
                target_filename = new_filename
            else:
                target_filename = source_path.name
            
            target_path = target_dir / target_filename
            
            # 如果目标文件已存在，生成唯一文件名
            if target_path.exists():
                target_filename = self._get_unique_filename(str(target_dir), target_filename)
                target_path = target_dir / target_filename
            
            # 移动文件
            shutil.move(str(source_path), str(target_path))
            
            logger.info(f"文件移动成功: {source_path} -> {target_path}")
            
            return {
                "success": True,
                "source_path": str(source_path),
                "target_path": str(target_path),
                "filename": target_filename
            }
            
        except Exception as e:
            logger.error(f"文件移动失败: {source_path} -> {target_directory}, 错误: {str(e)}")
            return {
                "success": False,
                "error": f"文件移动失败: {str(e)}"
            }
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            file_path = Path(file_path)
            if file_path.exists():
                file_path.unlink()
                logger.info(f"文件删除成功: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"文件删除失败: {file_path}, 错误: {str(e)}")
            return False
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """清理临时文件"""
        try:
            temp_dir = Path(self.settings.UPLOAD_DIR) / "temp"
            if not temp_dir.exists():
                return 0
            
            current_time = datetime.now()
            deleted_count = 0
            
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    # 检查文件年龄
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    age_hours = (current_time - file_time).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        file_path.unlink()
                        deleted_count += 1
            
            logger.info(f"清理临时文件完成，删除 {deleted_count} 个文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理临时文件失败: {str(e)}")
            return 0
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {"exists": False}
            
            stat = file_path.stat()
            
            return {
                "exists": True,
                "path": str(file_path),
                "name": file_path.name,
                "size": stat.st_size,
                "created_time": datetime.fromtimestamp(stat.st_ctime),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "extension": file_path.suffix.lower(),
                "mime_type": mimetypes.guess_type(str(file_path))[0]
            }
            
        except Exception as e:
            logger.error(f"获取文件信息失败: {file_path}, 错误: {str(e)}")
            return {"exists": False, "error": str(e)}


# 全局文件处理器实例
_file_handler = None


def get_file_handler() -> FileHandler:
    """获取文件处理器实例（单例模式）"""
    global _file_handler
    if _file_handler is None:
        _file_handler = FileHandler()
    return _file_handler
