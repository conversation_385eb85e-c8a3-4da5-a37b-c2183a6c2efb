"""
任务管理模块
处理批量OCR任务的创建、管理、进度跟踪和结果存储
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path
import uuid
from loguru import logger

from app.core.ocr_engine import get_ocr_engine
from app.core.file_handler import get_file_handler
from app.config import get_settings


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class OCRTaskConfig:
    """OCR任务配置"""
    language: str = "ch"
    mode: str = "accurate"  # accurate, fast, handwriting
    output_format: str = "txt"  # txt, json, docx, pdf
    use_angle_cls: bool = True
    use_space_char: bool = True


@dataclass
class TaskResult:
    """单个文件的识别结果"""
    file_path: str
    file_name: str
    success: bool
    text: str = ""
    confidence: float = 0.0
    word_count: int = 0
    line_count: int = 0
    error: Optional[str] = None
    processing_time: float = 0.0
    details: List[Dict] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = []


@dataclass
class OCRTask:
    """OCR任务"""
    task_id: str
    name: str
    description: str
    status: TaskStatus
    priority: TaskPriority
    config: OCRTaskConfig
    file_paths: List[str]
    results: List[TaskResult]
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    total_files: int = 0
    processed_files: int = 0
    successful_files: int = 0
    failed_files: int = 0
    error_message: Optional[str] = None
    
    def __post_init__(self):
        self.total_files = len(self.file_paths)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.ocr_engine = get_ocr_engine()
        self.file_handler = get_file_handler()
        self.tasks: Dict[str, OCRTask] = {}
        self.task_queue: List[str] = []
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = 3
        
        # 确保任务存储目录存在
        self.tasks_dir = Path(self.settings.RESULTS_DIR) / "tasks"
        self.tasks_dir.mkdir(parents=True, exist_ok=True)
    
    def create_task(self, name: str, description: str, file_paths: List[str],
                   config: OCRTaskConfig, priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """创建新任务"""
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 验证文件路径
            valid_files = []
            for file_path in file_paths:
                if self.ocr_engine.validate_image(file_path):
                    valid_files.append(file_path)
                else:
                    logger.warning(f"无效的图片文件: {file_path}")
            
            if not valid_files:
                raise ValueError("没有有效的图片文件")
            
            # 创建任务对象
            task = OCRTask(
                task_id=task_id,
                name=name,
                description=description,
                status=TaskStatus.PENDING,
                priority=priority,
                config=config,
                file_paths=valid_files,
                results=[],
                created_at=datetime.now()
            )
            
            # 保存任务
            self.tasks[task_id] = task
            self._save_task_to_disk(task)
            
            # 添加到队列
            self._add_to_queue(task_id)
            
            logger.info(f"任务创建成功: {task_id}, 文件数量: {len(valid_files)}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise
    
    def _add_to_queue(self, task_id: str):
        """将任务添加到队列"""
        task = self.tasks[task_id]
        
        # 根据优先级插入队列
        if task.priority == TaskPriority.URGENT:
            self.task_queue.insert(0, task_id)
        elif task.priority == TaskPriority.HIGH:
            # 插入到高优先级任务之后
            insert_pos = 0
            for i, tid in enumerate(self.task_queue):
                if self.tasks[tid].priority != TaskPriority.URGENT:
                    insert_pos = i
                    break
            self.task_queue.insert(insert_pos, task_id)
        else:
            self.task_queue.append(task_id)
    
    async def start_task_processing(self):
        """开始处理任务队列"""
        while True:
            # 检查是否有可处理的任务
            if (len(self.running_tasks) < self.max_concurrent_tasks and 
                self.task_queue):
                
                task_id = self.task_queue.pop(0)
                if task_id in self.tasks:
                    # 启动任务处理
                    task_coroutine = self._process_task(task_id)
                    self.running_tasks[task_id] = asyncio.create_task(task_coroutine)
            
            # 清理已完成的任务
            completed_tasks = []
            for task_id, task_coroutine in self.running_tasks.items():
                if task_coroutine.done():
                    completed_tasks.append(task_id)
            
            for task_id in completed_tasks:
                del self.running_tasks[task_id]
            
            # 短暂休眠
            await asyncio.sleep(1)
    
    async def _process_task(self, task_id: str):
        """处理单个任务"""
        task = self.tasks[task_id]
        
        try:
            logger.info(f"开始处理任务: {task_id}")
            
            # 更新任务状态
            task.status = TaskStatus.PROCESSING
            task.started_at = datetime.now()
            self._save_task_to_disk(task)
            
            # 定义进度回调函数
            async def progress_callback(progress: float, processed: int, total: int, result: Dict):
                task.progress = progress
                task.processed_files = processed
                
                # 创建任务结果对象
                task_result = TaskResult(
                    file_path=result["file_path"],
                    file_name=result["file_name"],
                    success=result["success"],
                    text=result.get("text", ""),
                    confidence=result.get("confidence", 0.0),
                    word_count=result.get("word_count", 0),
                    line_count=result.get("line_count", 0),
                    error=result.get("error"),
                    details=result.get("details", [])
                )
                
                task.results.append(task_result)
                
                if result["success"]:
                    task.successful_files += 1
                else:
                    task.failed_files += 1
                
                # 保存进度
                self._save_task_to_disk(task)
                
                logger.info(f"任务 {task_id} 进度: {progress:.1f}% ({processed}/{total})")
            
            # 执行批量OCR识别
            results = await self.ocr_engine.recognize_batch_images(
                task.file_paths, 
                progress_callback
            )
            
            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.progress = 100.0
            
            logger.info(f"任务处理完成: {task_id}, 成功: {task.successful_files}, 失败: {task.failed_files}")
            
        except Exception as e:
            logger.error(f"任务处理失败: {task_id}, 错误: {str(e)}")
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
        
        finally:
            self._save_task_to_disk(task)
    
    def get_task(self, task_id: str) -> Optional[OCRTask]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[OCRTask]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[OCRTask]:
        """根据状态获取任务"""
        return [task for task in self.tasks.values() if task.status == status]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            if task_id in self.running_tasks:
                # 取消正在运行的任务
                self.running_tasks[task_id].cancel()
                del self.running_tasks[task_id]
            
            if task_id in self.task_queue:
                # 从队列中移除
                self.task_queue.remove(task_id)
            
            if task_id in self.tasks:
                # 更新任务状态
                task = self.tasks[task_id]
                task.status = TaskStatus.CANCELLED
                task.completed_at = datetime.now()
                self._save_task_to_disk(task)
            
            logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        try:
            # 先取消任务
            self.cancel_task(task_id)
            
            # 删除任务文件
            task_file = self.tasks_dir / f"{task_id}.json"
            if task_file.exists():
                task_file.unlink()
            
            # 从内存中删除
            if task_id in self.tasks:
                del self.tasks[task_id]
            
            logger.info(f"任务已删除: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def _save_task_to_disk(self, task: OCRTask):
        """将任务保存到磁盘"""
        try:
            task_file = self.tasks_dir / f"{task.task_id}.json"
            
            # 转换为可序列化的字典
            task_dict = asdict(task)
            
            # 处理日期时间字段
            for field in ['created_at', 'started_at', 'completed_at']:
                if task_dict[field]:
                    task_dict[field] = task_dict[field].isoformat()
            
            # 处理枚举字段
            task_dict['status'] = task.status.value
            task_dict['priority'] = task.priority.value
            
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(task_dict, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存任务到磁盘失败: {task.task_id}, 错误: {str(e)}")
    
    def load_tasks_from_disk(self):
        """从磁盘加载任务"""
        try:
            for task_file in self.tasks_dir.glob("*.json"):
                with open(task_file, 'r', encoding='utf-8') as f:
                    task_dict = json.load(f)
                
                # 恢复日期时间字段
                for field in ['created_at', 'started_at', 'completed_at']:
                    if task_dict[field]:
                        task_dict[field] = datetime.fromisoformat(task_dict[field])
                
                # 恢复枚举字段
                task_dict['status'] = TaskStatus(task_dict['status'])
                task_dict['priority'] = TaskPriority(task_dict['priority'])
                
                # 恢复配置对象
                task_dict['config'] = OCRTaskConfig(**task_dict['config'])
                
                # 恢复结果对象
                results = []
                for result_dict in task_dict['results']:
                    results.append(TaskResult(**result_dict))
                task_dict['results'] = results
                
                # 创建任务对象
                task = OCRTask(**task_dict)
                self.tasks[task.task_id] = task
                
                # 如果任务还在处理中，重新加入队列
                if task.status == TaskStatus.PROCESSING:
                    task.status = TaskStatus.PENDING
                    self._add_to_queue(task.task_id)
            
            logger.info(f"从磁盘加载了 {len(self.tasks)} 个任务")
            
        except Exception as e:
            logger.error(f"从磁盘加载任务失败: {str(e)}")


# 全局任务管理器实例
_task_manager = None


def get_task_manager() -> TaskManager:
    """获取任务管理器实例（单例模式）"""
    global _task_manager
    if _task_manager is None:
        _task_manager = TaskManager()
        _task_manager.load_tasks_from_disk()
    return _task_manager
