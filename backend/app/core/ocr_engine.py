"""
OCR引擎核心模块
基于PaddleOCR实现文字识别功能
"""

import numpy as np
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from PIL import Image
import asyncio
import concurrent.futures
import os
import signal
import time
import re
from contextlib import contextmanager
from loguru import logger

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    logger.warning("PaddleOCR not installed. Using mock OCR for testing.")
    PADDLEOCR_AVAILABLE = False

    # Mock PaddleOCR class for testing
    class PaddleOCR:
        def __init__(self, **kwargs):
            pass

        def ocr(self, image, cls=True):
            # Return mock OCR result
            return [[
                [[[10, 10], [100, 10], [100, 30], [10, 30]], ("这是模拟的OCR识别结果", 0.95)],
                [[[10, 40], [120, 40], [120, 60], [10, 60]], ("用于测试系统功能", 0.92)]
            ]]

from app.config import get_settings


@contextmanager
def timeout_handler(seconds):
    """超时控制上下文管理器"""
    def timeout_signal_handler(signum, frame):
        raise TimeoutError(f"操作超时 ({seconds}秒)")

    # 设置信号处理器
    old_handler = signal.signal(signal.SIGALRM, timeout_signal_handler)
    signal.alarm(seconds)

    try:
        yield
    finally:
        # 恢复原来的信号处理器
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)


class OCREngine:
    """OCR识别引擎"""
    
    def __init__(self):
        self.settings = get_settings()
        self._ocr_instance = None
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        
    def _check_model_cache(self) -> bool:
        """检查模型是否已缓存"""
        cache_dir = Path.home() / ".paddlex" / "official_models"
        if not cache_dir.exists():
            return False

        # 检查是否有模型文件
        model_dirs = [d for d in cache_dir.iterdir() if d.is_dir()]
        return len(model_dirs) >= 4  # 至少需要4个模型目录

    def _get_ocr_instance(self) -> PaddleOCR:
        """获取OCR实例（单例模式）"""
        if self._ocr_instance is None:
            # 检查模型缓存状态
            has_cache = self._check_model_cache()
            if has_cache:
                logger.info("检测到模型缓存，快速初始化OCR引擎...")
            else:
                logger.warning("未检测到模型缓存，首次启动需要下载模型，请耐心等待1-2分钟...")
                logger.info("提示: 可以运行 'python backend/scripts/download_models.py' 预下载模型")

            start_time = time.time()

            # 设置模型缓存目录
            model_dir = os.path.expanduser("~/.paddlex/official_models")
            os.makedirs(model_dir, exist_ok=True)

            # 使用最简单的参数初始化PaddleOCR
            self._ocr_instance = PaddleOCR(
                use_textline_orientation=self.settings.OCR_USE_ANGLE_CLS,
                lang=self.settings.OCR_LANG
            )

            init_time = time.time() - start_time
            if has_cache:
                logger.info(f"OCR引擎初始化完成，耗时: {init_time:.2f}秒")
            else:
                logger.info(f"OCR引擎初始化完成，模型下载+初始化耗时: {init_time:.2f}秒")
                logger.info("下次启动将会更快！")

        return self._ocr_instance
    
    def _preprocess_image(self, image_path: Union[str, Path]) -> np.ndarray:
        """图片预处理 - 智能压缩大图片"""
        try:
            # 读取图片
            if isinstance(image_path, str):
                image_path = Path(image_path)

            # 使用PIL读取图片以支持更多格式
            pil_image = Image.open(image_path)
            original_width, original_height = pil_image.size

            # 获取文件大小
            file_size_mb = image_path.stat().st_size / (1024 * 1024)

            logger.info(f"原始图片信息: {original_width}x{original_height}, 文件大小: {file_size_mb:.2f}MB")

            # 转换为RGB格式
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            # 智能图片压缩策略
            max_size = 1600  # 降低最大边长以提高处理速度
            max_file_size_mb = 3  # 最大文件大小限制

            # 如果文件太大或尺寸太大，进行压缩
            needs_resize = (max(original_width, original_height) > max_size or
                          file_size_mb > max_file_size_mb)

            if needs_resize:
                # 计算压缩比例
                if max(original_width, original_height) > max_size:
                    scale = max_size / max(original_width, original_height)
                else:
                    # 基于文件大小计算压缩比例
                    scale = min(1.0, (max_file_size_mb / file_size_mb) ** 0.5)

                new_width = int(original_width * scale)
                new_height = int(original_height * scale)

                # 确保最小尺寸
                min_size = 400
                if new_width < min_size or new_height < min_size:
                    scale = min_size / min(original_width, original_height)
                    new_width = int(original_width * scale)
                    new_height = int(original_height * scale)

                # 使用更快的重采样方法
                resample_method = Image.Resampling.BILINEAR  # 比LANCZOS更快
                pil_image = pil_image.resize((new_width, new_height), resample_method)
                logger.info(f"图片智能压缩: {original_width}x{original_height} -> {new_width}x{new_height} (压缩比: {scale:.2f})")
            else:
                logger.info(f"图片尺寸适中，无需压缩: {original_width}x{original_height}")

            # 转换为numpy数组
            image = np.array(pil_image)

            # 如果需要BGR格式，手动转换
            if PADDLEOCR_AVAILABLE:
                # PaddleOCR需要BGR格式，手动转换RGB到BGR
                image = image[:, :, ::-1]  # RGB to BGR

            return image

        except Exception as e:
            logger.error(f"图片预处理失败: {image_path}, 错误: {str(e)}")
            raise
    
    def _extract_text_from_result(self, ocr_result: List) -> Dict[str, Any]:
        """从OCR结果中提取文本信息"""
        if not ocr_result:
            return {
                "text": "",
                "confidence": 0.0,
                "word_count": 0,
                "line_count": 0,
                "details": []
            }

        texts = []
        confidences = []
        details = []

        # 处理新版本PaddleOCR的结果格式
        # ocr.ocr() 返回的格式: [OCRResult对象]
        for i, page_result in enumerate(ocr_result):
            if page_result is None:
                continue

            # 检查是否是新版本的OCRResult对象
            # 尝试多种方式访问属性
            try:
                # 方法1: 尝试字典访问
                if hasattr(page_result, '__getitem__'):
                    try:
                        rec_texts = page_result['rec_texts']
                        rec_scores = page_result['rec_scores']
                    except (KeyError, TypeError):
                        rec_texts = None
                        rec_scores = None
                else:
                    rec_texts = None
                    rec_scores = None

                # 方法2: 如果字典访问失败，尝试属性访问
                if rec_texts is None:
                    rec_texts = getattr(page_result, 'rec_texts', None)
                    rec_scores = getattr(page_result, 'rec_scores', None)

                if rec_texts is not None and rec_scores is not None:
                    # 新版本格式 - OCRResult对象
                    # 尝试获取rec_polys
                    try:
                        if hasattr(page_result, '__getitem__'):
                            rec_polys = page_result.get('rec_polys', [])
                        else:
                            rec_polys = getattr(page_result, 'rec_polys', [])
                    except:
                        rec_polys = []

                    if rec_texts:
                        for j, text in enumerate(rec_texts):
                            confidence = rec_scores[j] if j < len(rec_scores) else 1.0
                            # rec_polys可能是字符串格式，需要特殊处理
                            bbox = []
                            if rec_polys and j < len(rec_polys):
                                try:
                                    poly = rec_polys[j]
                                    if hasattr(poly, 'tolist'):
                                        bbox = poly.tolist()
                                    elif isinstance(poly, str):
                                        # 字符串格式的坐标，暂时设为空列表
                                        bbox = []
                                    else:
                                        bbox = poly
                                except:
                                    bbox = []

                            texts.append(text)
                            confidences.append(confidence)
                            details.append({
                                "text": text,
                                "confidence": confidence,
                                "bbox": bbox
                            })
                    break  # 新版本格式处理完成，跳出循环
                else:
                    # 兼容旧版本格式（如果新版本处理失败）
                    if isinstance(page_result, (list, tuple)):
                        for line_result in page_result:
                            if isinstance(line_result, (list, tuple)) and len(line_result) >= 2:
                                bbox = line_result[0]  # 边界框坐标
                                text_info = line_result[1]  # 文本和置信度

                                if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                    text = text_info[0]
                                    confidence = text_info[1]
                                else:
                                    text = str(text_info)
                                    confidence = 1.0

                                texts.append(text)
                                confidences.append(confidence)
                                details.append({
                                    "text": text,
                                    "confidence": confidence,
                                    "bbox": bbox
                                })
            except Exception as e:
                logger.error(f"处理OCR结果失败: {e}")
                continue
        
        # 合并所有文本
        full_text = "\n".join(texts)
        
        # 计算平均置信度
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        return {
            "text": full_text,
            "confidence": round(avg_confidence, 4),
            "word_count": len(full_text.replace(" ", "").replace("\n", "")),
            "line_count": len(texts),
            "details": details
        }
    
    def recognize_single_image(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """识别单张图片"""
        try:
            logger.info(f"开始识别图片: {image_path}")

            # 预处理图片
            image = self._preprocess_image(image_path)

            # 执行OCR识别，添加超时控制
            ocr = self._get_ocr_instance()

            # 使用超时控制 - 根据图片大小动态调整
            file_size_mb = Path(image_path).stat().st_size / (1024 * 1024)
            if file_size_mb > 5:
                timeout_seconds = 30  # 大文件30秒超时
            elif file_size_mb > 2:
                timeout_seconds = 20  # 中等文件20秒超时
            else:
                timeout_seconds = 15  # 小文件15秒超时

            logger.info(f"设置OCR超时时间: {timeout_seconds}秒 (文件大小: {file_size_mb:.2f}MB)")

            with timeout_handler(timeout_seconds):
                # 使用预处理后的图片数据而不是原始路径
                result = ocr.ocr(image)

            # 提取文本信息
            if result and len(result) > 0:
                text_info = self._extract_text_from_result(result)
            else:
                text_info = self._extract_text_from_result([])

            logger.info(f"图片识别完成: {image_path}, 识别到 {text_info['line_count']} 行文本")

            return {
                "success": True,
                "file_path": str(image_path),
                "file_name": Path(image_path).name,
                **text_info
            }

        except TimeoutError as e:
            logger.error(f"图片识别超时: {image_path}, 错误: {str(e)}")
            return {
                "success": False,
                "file_path": str(image_path),
                "file_name": Path(image_path).name,
                "error": f"识别超时: {str(e)}",
                "text": "",
                "confidence": 0.0,
                "word_count": 0,
                "line_count": 0,
                "details": []
            }
        except Exception as e:
            logger.error(f"图片识别失败: {image_path}, 错误: {str(e)}")
            return {
                "success": False,
                "file_path": str(image_path),
                "file_name": Path(image_path).name,
                "error": str(e),
                "text": "",
                "confidence": 0.0,
                "word_count": 0,
                "line_count": 0,
                "details": []
            }
    
    async def recognize_single_image_async(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """异步识别单张图片"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor, 
            self.recognize_single_image, 
            image_path
        )
    
    async def recognize_batch_images(self, image_paths: List[Union[str, Path]], 
                                   progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """批量识别图片"""
        logger.info(f"开始批量识别 {len(image_paths)} 张图片")
        
        results = []
        total = len(image_paths)
        
        # 创建异步任务
        tasks = []
        for i, image_path in enumerate(image_paths):
            task = self.recognize_single_image_async(image_path)
            tasks.append(task)
        
        # 执行任务并收集结果
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            results.append(result)
            
            # 调用进度回调
            if progress_callback:
                progress = (i + 1) / total * 100
                await progress_callback(progress, i + 1, total, result)
        
        logger.info(f"批量识别完成，成功: {sum(1 for r in results if r['success'])}, "
                   f"失败: {sum(1 for r in results if not r['success'])}")
        
        return results
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的图片格式"""
        return [".jpg", ".jpeg", ".png", ".bmp", ".webp", ".tiff", ".tif"]
    
    def validate_image(self, image_path: Union[str, Path]) -> bool:
        """验证图片是否有效"""
        try:
            image_path = Path(image_path)

            # 检查文件是否存在
            if not image_path.exists():
                return False

            # 检查文件扩展名
            if image_path.suffix.lower() not in self.get_supported_formats():
                return False

            # 尝试打开图片
            with Image.open(image_path) as img:
                img.verify()

            return True

        except Exception:
            return False

    def smart_format_text(self, text: str) -> str:
        """智能排版文本为key:value格式"""
        if not text or not text.strip():
            return text

        lines = text.strip().split('\n')

        # 过滤掉logo图标和无用内容
        filtered_lines = []
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 过滤掉logo图标相关内容（通常是单个字符或特殊符号）
            if len(line) == 1 and not line.isalnum():
                continue

            # 过滤掉常见的logo文字
            if line in ['LOGO', 'logo', '图标', '标识', '商标', '®', '©', '™']:
                continue

            # 过滤掉纯符号行
            if re.match(r'^[^\w\u4e00-\u9fa5]+$', line):
                continue

            filtered_lines.append(line)

        # 预处理：合并可能被分割的内容
        processed_lines = []
        i = 0
        while i < len(filtered_lines):
            line = filtered_lines[i].strip()
            if not line:
                i += 1
                continue

            # 检查是否是被分割的日期（如"日"和"期"分开）
            if line in ['日', '期'] and i + 1 < len(filtered_lines) and filtered_lines[i + 1].strip() in ['期', '日']:
                processed_lines.append('日期')
                i += 2
                continue

            # 检查是否是被分割的年月（如"2013"和"12"分开）
            if re.match(r'^\d{4}$', line) and i + 1 < len(filtered_lines) and re.match(r'^\d{1,2}$', filtered_lines[i + 1].strip()):
                processed_lines.append(f"{line}-{filtered_lines[i + 1].strip()}")
                i += 2
                continue

            processed_lines.append(line)
            i += 1

        # 分类存储不同类型的信息
        organization_info = []  # 机构信息
        device_info = []       # 设备信息
        other_info = []        # 其他信息

        # 智能识别和分类（支持上下文关联）
        i = 0
        while i < len(processed_lines):
            line = processed_lines[i]
            if not line:
                i += 1
                continue

            # 检查是否已经是key:value格式
            if ':' in line or '：' in line:
                # 清理格式
                if '：' in line:
                    parts = line.split('：', 1)
                else:
                    parts = line.split(':', 1)

                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    if key and value:
                        # 根据key类型分类存储
                        if key in ['机构', '单位', '部门', '公司', '组织', '管理员', '管理单位']:
                            organization_info.append(f"{key}：{value}")
                        else:
                            device_info.append(f"{key}：{value}")
                        i += 1
                        continue

            # 检查是否是标签词，如果是，尝试与下一行组合
            next_line = processed_lines[i + 1] if i + 1 < len(processed_lines) else None

            if line in ['用途', '管理员', '日期', '型号', '编号', '名称', '品牌', '类型', '规格', '接口', '容量', '速度', '位置', '状态', '备注', '描述', '功能', '版本', '配置'] and next_line:
                # 标签词与下一行组合
                if line in ['管理员', '管理单位']:
                    organization_info.append(f"{line}：{next_line}")
                else:
                    device_info.append(f"{line}：{next_line}")
                i += 2
                continue

            # 智能分类识别
            classified = False

            # 1. 品牌识别
            if line.upper() in ['DELL', 'HP', 'IBM', 'LENOVO', 'ASUS', 'ACER', 'APPLE', 'SAMSUNG', 'SONY', 'TOSHIBA', 'FUJITSU', 'NEC', 'PANASONIC', 'CISCO', 'HUAWEI', 'ZTE', 'XIAOMI', 'OPPO', 'VIVO', 'ONEPLUS']:
                formatted_lines.append(f"品牌：{line}")
                classified = True

            # 2. 型号识别（字母+数字组合）
            elif re.match(r'^[A-Z]+\d+[A-Z]*$', line.upper()) and len(line) <= 20:
                formatted_lines.append(f"型号：{line}")
                classified = True

            # 3. 编号识别（包含字母数字和特殊字符的组合）
            elif re.match(r'^[A-Z0-9\-_]+$', line.upper()) and len(line) > 5 and len(line) <= 30:
                formatted_lines.append(f"编号：{line}")
                classified = True

            # 4. 设备类型识别
            elif line in ['服务器', '交换机', '路由器', '防火墙', '存储', '工作站', '笔记本', '台式机', '显示器', '打印机', '扫描仪', '投影仪', '摄像头', '音响', '键盘', '鼠标']:
                formatted_lines.append(f"类型：{line}")
                classified = True

            # 5. 容量/规格识别
            elif re.search(r'\d+\s*(GB|TB|MB|KB|rpm|Hz|MHz|GHz|V|A|W|Ω|°C|°F|mm|cm|m|km|g|kg|t|l|ml|k)', line, re.IGNORECASE):
                formatted_lines.append(f"规格：{line}")
                classified = True

            # 6. 日期识别
            elif re.search(r'\d{4}[-/年]\d{1,2}[-/月]?\d{0,2}[日]?', line) or line == '日期' or re.match(r'^\d{4}-\d{1,2}$', line):
                formatted_lines.append(f"日期：{line}")
                classified = True

            # 7. 接口类型识别
            elif line.upper() in ['SAS', 'SATA', 'USB', 'HDMI', 'VGA', 'DVI', 'DP', 'RJ45', 'WIFI', 'BLUETOOTH', 'ETHERNET', 'FIBER', 'SCSI', 'IDE', 'PCIE', 'PCI']:
                formatted_lines.append(f"接口：{line}")
                classified = True

            # 8. 用途/功能识别
            elif any(keyword in line for keyword in ['代理', '服务', '管理', '监控', '备份', '存储', '计算', '网络', '安全', '数据库', 'WEB', 'FTP', 'DNS', 'DHCP']):
                formatted_lines.append(f"用途：{line}")
                classified = True

            # 9. 组织机构识别
            elif any(suffix in line for suffix in ['局', '处', '科', '室', '组', '队', '中心', '站', '所', '院', '校', '馆', '厅', '委', '会', '办', '署', '部', '省', '市', '县', '区', '镇', '村', '公司', '企业', '集团']):
                formatted_lines.append(f"机构：{line}")
                classified = True

            # 10. 人名识别（中文姓名模式）
            elif re.match(r'^[\u4e00-\u9fa5]{2,4}$', line) and not any(char in line for char in ['局', '处', '科', '室', '组', '队', '中心', '站', '所', '院', '校', '馆', '厅', '委', '会', '办', '署', '部', '省', '市', '县', '区', '镇', '村']):
                formatted_lines.append(f"管理员：{line}")
                classified = True

            # 11. 版本号识别
            elif re.search(r'v?\d+\.\d+', line, re.IGNORECASE):
                formatted_lines.append(f"版本：{line}")
                classified = True

            # 12. 默认分类
            if not classified:
                if len(line) <= 3:
                    # 很短的文本，可能是代码或简称
                    formatted_lines.append(f"代码：{line}")
                elif len(line) <= 20 and any(char.isdigit() for char in line):
                    # 包含数字的短文本
                    formatted_lines.append(f"标识：{line}")
                elif len(line) <= 20:
                    # 纯文本短内容
                    formatted_lines.append(f"名称：{line}")
                else:
                    # 长文本
                    formatted_lines.append(f"描述：{line}")

            i += 1

        return '\n'.join(formatted_lines)


# 全局OCR引擎实例
_ocr_engine = None


def get_ocr_engine() -> OCREngine:
    """获取OCR引擎实例（单例模式）"""
    global _ocr_engine
    if _ocr_engine is None:
        _ocr_engine = OCREngine()
    return _ocr_engine
