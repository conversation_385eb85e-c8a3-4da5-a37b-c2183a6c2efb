"""
OCR引擎核心模块
基于PaddleOCR实现文字识别功能
"""

import numpy as np
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from PIL import Image
import asyncio
import concurrent.futures
import os
import signal
from contextlib import contextmanager
from loguru import logger

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    logger.warning("PaddleOCR not installed. Using mock OCR for testing.")
    PADDLEOCR_AVAILABLE = False

    # Mock PaddleOCR class for testing
    class PaddleOCR:
        def __init__(self, **kwargs):
            pass

        def ocr(self, image, cls=True):
            # Return mock OCR result
            return [[
                [[[10, 10], [100, 10], [100, 30], [10, 30]], ("这是模拟的OCR识别结果", 0.95)],
                [[[10, 40], [120, 40], [120, 60], [10, 60]], ("用于测试系统功能", 0.92)]
            ]]

from app.config import get_settings


@contextmanager
def timeout_handler(seconds):
    """超时控制上下文管理器"""
    def timeout_signal_handler(signum, frame):
        raise TimeoutError(f"操作超时 ({seconds}秒)")

    # 设置信号处理器
    old_handler = signal.signal(signal.SIGALRM, timeout_signal_handler)
    signal.alarm(seconds)

    try:
        yield
    finally:
        # 恢复原来的信号处理器
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)


class OCREngine:
    """OCR识别引擎"""
    
    def __init__(self):
        self.settings = get_settings()
        self._ocr_instance = None
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        
    def _get_ocr_instance(self) -> PaddleOCR:
        """获取OCR实例（单例模式）"""
        if self._ocr_instance is None:
            logger.info("初始化OCR引擎...")

            # 设置模型缓存目录，避免重复下载
            model_dir = os.path.expanduser("~/.paddlex/official_models")
            os.makedirs(model_dir, exist_ok=True)

            # 使用最简单的参数初始化PaddleOCR
            self._ocr_instance = PaddleOCR(
                use_textline_orientation=self.settings.OCR_USE_ANGLE_CLS,
                lang=self.settings.OCR_LANG
            )
            logger.info("OCR引擎初始化完成")
        return self._ocr_instance
    
    def _preprocess_image(self, image_path: Union[str, Path]) -> np.ndarray:
        """图片预处理"""
        try:
            # 读取图片
            if isinstance(image_path, str):
                image_path = Path(image_path)

            # 使用PIL读取图片以支持更多格式
            pil_image = Image.open(image_path)

            # 转换为RGB格式
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            # 图片尺寸优化 - 如果图片太大，进行缩放以提高识别速度
            max_size = 2048  # 最大边长
            width, height = pil_image.size

            if max(width, height) > max_size:
                # 计算缩放比例
                scale = max_size / max(width, height)
                new_width = int(width * scale)
                new_height = int(height * scale)

                # 使用高质量重采样
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                logger.info(f"图片尺寸优化: {width}x{height} -> {new_width}x{new_height}")

            # 转换为numpy数组
            image = np.array(pil_image)

            # 如果需要BGR格式，手动转换
            if PADDLEOCR_AVAILABLE:
                # PaddleOCR需要BGR格式，手动转换RGB到BGR
                image = image[:, :, ::-1]  # RGB to BGR

            return image

        except Exception as e:
            logger.error(f"图片预处理失败: {image_path}, 错误: {str(e)}")
            raise
    
    def _extract_text_from_result(self, ocr_result: List) -> Dict[str, Any]:
        """从OCR结果中提取文本信息"""
        if not ocr_result:
            return {
                "text": "",
                "confidence": 0.0,
                "word_count": 0,
                "line_count": 0,
                "details": []
            }

        texts = []
        confidences = []
        details = []

        # 处理新版本PaddleOCR的结果格式
        # ocr.ocr() 返回的格式: [OCRResult对象]
        for i, page_result in enumerate(ocr_result):
            if page_result is None:
                continue

            # 检查是否是新版本的OCRResult对象
            # 尝试多种方式访问属性
            try:
                # 方法1: 尝试字典访问
                if hasattr(page_result, '__getitem__'):
                    try:
                        rec_texts = page_result['rec_texts']
                        rec_scores = page_result['rec_scores']
                    except (KeyError, TypeError):
                        rec_texts = None
                        rec_scores = None
                else:
                    rec_texts = None
                    rec_scores = None

                # 方法2: 如果字典访问失败，尝试属性访问
                if rec_texts is None:
                    rec_texts = getattr(page_result, 'rec_texts', None)
                    rec_scores = getattr(page_result, 'rec_scores', None)

                if rec_texts is not None and rec_scores is not None:
                    # 新版本格式 - OCRResult对象
                    # 尝试获取rec_polys
                    try:
                        if hasattr(page_result, '__getitem__'):
                            rec_polys = page_result.get('rec_polys', [])
                        else:
                            rec_polys = getattr(page_result, 'rec_polys', [])
                    except:
                        rec_polys = []

                    if rec_texts:
                        for j, text in enumerate(rec_texts):
                            confidence = rec_scores[j] if j < len(rec_scores) else 1.0
                            # rec_polys可能是字符串格式，需要特殊处理
                            bbox = []
                            if rec_polys and j < len(rec_polys):
                                try:
                                    poly = rec_polys[j]
                                    if hasattr(poly, 'tolist'):
                                        bbox = poly.tolist()
                                    elif isinstance(poly, str):
                                        # 字符串格式的坐标，暂时设为空列表
                                        bbox = []
                                    else:
                                        bbox = poly
                                except:
                                    bbox = []

                            texts.append(text)
                            confidences.append(confidence)
                            details.append({
                                "text": text,
                                "confidence": confidence,
                                "bbox": bbox
                            })
                    break  # 新版本格式处理完成，跳出循环
                else:
                    # 兼容旧版本格式（如果新版本处理失败）
                    if isinstance(page_result, (list, tuple)):
                        for line_result in page_result:
                            if isinstance(line_result, (list, tuple)) and len(line_result) >= 2:
                                bbox = line_result[0]  # 边界框坐标
                                text_info = line_result[1]  # 文本和置信度

                                if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                    text = text_info[0]
                                    confidence = text_info[1]
                                else:
                                    text = str(text_info)
                                    confidence = 1.0

                                texts.append(text)
                                confidences.append(confidence)
                                details.append({
                                    "text": text,
                                    "confidence": confidence,
                                    "bbox": bbox
                                })
            except Exception as e:
                logger.error(f"处理OCR结果失败: {e}")
                continue
        
        # 合并所有文本
        full_text = "\n".join(texts)
        
        # 计算平均置信度
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        return {
            "text": full_text,
            "confidence": round(avg_confidence, 4),
            "word_count": len(full_text.replace(" ", "").replace("\n", "")),
            "line_count": len(texts),
            "details": details
        }
    
    def recognize_single_image(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """识别单张图片"""
        try:
            logger.info(f"开始识别图片: {image_path}")

            # 预处理图片
            image = self._preprocess_image(image_path)

            # 执行OCR识别，添加超时控制
            ocr = self._get_ocr_instance()

            # 使用超时控制
            timeout_seconds = getattr(self.settings, 'OCR_TIMEOUT', 300)  # 默认5分钟

            with timeout_handler(timeout_seconds):
                # 使用正确的ocr方法而不是predict
                result = ocr.ocr(str(image_path))

            # 提取文本信息
            if result and len(result) > 0:
                text_info = self._extract_text_from_result(result)
            else:
                text_info = self._extract_text_from_result([])

            logger.info(f"图片识别完成: {image_path}, 识别到 {text_info['line_count']} 行文本")

            return {
                "success": True,
                "file_path": str(image_path),
                "file_name": Path(image_path).name,
                **text_info
            }

        except TimeoutError as e:
            logger.error(f"图片识别超时: {image_path}, 错误: {str(e)}")
            return {
                "success": False,
                "file_path": str(image_path),
                "file_name": Path(image_path).name,
                "error": f"识别超时: {str(e)}",
                "text": "",
                "confidence": 0.0,
                "word_count": 0,
                "line_count": 0,
                "details": []
            }
        except Exception as e:
            logger.error(f"图片识别失败: {image_path}, 错误: {str(e)}")
            return {
                "success": False,
                "file_path": str(image_path),
                "file_name": Path(image_path).name,
                "error": str(e),
                "text": "",
                "confidence": 0.0,
                "word_count": 0,
                "line_count": 0,
                "details": []
            }
    
    async def recognize_single_image_async(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """异步识别单张图片"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor, 
            self.recognize_single_image, 
            image_path
        )
    
    async def recognize_batch_images(self, image_paths: List[Union[str, Path]], 
                                   progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """批量识别图片"""
        logger.info(f"开始批量识别 {len(image_paths)} 张图片")
        
        results = []
        total = len(image_paths)
        
        # 创建异步任务
        tasks = []
        for i, image_path in enumerate(image_paths):
            task = self.recognize_single_image_async(image_path)
            tasks.append(task)
        
        # 执行任务并收集结果
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            results.append(result)
            
            # 调用进度回调
            if progress_callback:
                progress = (i + 1) / total * 100
                await progress_callback(progress, i + 1, total, result)
        
        logger.info(f"批量识别完成，成功: {sum(1 for r in results if r['success'])}, "
                   f"失败: {sum(1 for r in results if not r['success'])}")
        
        return results
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的图片格式"""
        return [".jpg", ".jpeg", ".png", ".bmp", ".webp", ".tiff", ".tif"]
    
    def validate_image(self, image_path: Union[str, Path]) -> bool:
        """验证图片是否有效"""
        try:
            image_path = Path(image_path)
            
            # 检查文件是否存在
            if not image_path.exists():
                return False
            
            # 检查文件扩展名
            if image_path.suffix.lower() not in self.get_supported_formats():
                return False
            
            # 尝试打开图片
            with Image.open(image_path) as img:
                img.verify()
            
            return True
            
        except Exception:
            return False


# 全局OCR引擎实例
_ocr_engine = None


def get_ocr_engine() -> OCREngine:
    """获取OCR引擎实例（单例模式）"""
    global _ocr_engine
    if _ocr_engine is None:
        _ocr_engine = OCREngine()
    return _ocr_engine
