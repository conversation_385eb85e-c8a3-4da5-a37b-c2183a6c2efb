"""
API数据模型（Pydantic schemas）
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class OCRConfigSchema(BaseModel):
    """OCR配置模型"""
    language: str = Field(default="ch", description="识别语言")
    mode: str = Field(default="accurate", description="识别模式")
    output_format: str = Field(default="txt", description="输出格式")
    use_angle_cls: bool = Field(default=True, description="是否使用角度分类")
    use_space_char: bool = Field(default=True, description="是否使用空格字符")


class TaskCreateSchema(BaseModel):
    """创建任务请求模型"""
    name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    priority: int = Field(default=2, description="任务优先级")
    config: OCRConfigSchema = Field(..., description="OCR配置")


class FileInfoSchema(BaseModel):
    """文件信息模型"""
    id: Optional[int] = None
    original_filename: str
    stored_filename: str
    file_path: str
    file_size: int
    file_hash: str
    mime_type: str
    extension: str
    status: str
    uploaded_at: datetime
    
    class Config:
        from_attributes = True


class OCRResultDetailSchema(BaseModel):
    """OCR识别详细结果模型"""
    text: str
    confidence: float
    bbox: List[List[float]]


class OCRResultSchema(BaseModel):
    """OCR识别结果模型"""
    id: Optional[int] = None
    task_id: str
    file_id: int
    text_content: str
    confidence: float
    word_count: int
    line_count: int
    details: Optional[List[Dict[str, Any]]] = None
    processing_time: float
    success: bool
    error_message: Optional[str] = None
    is_edited: bool = False
    original_text: Optional[str] = None
    edited_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TaskSchema(BaseModel):
    """任务模型"""
    id: Optional[int] = None
    task_id: str
    name: str
    description: Optional[str] = None
    status: str
    priority: int
    language: str
    mode: str
    output_format: str
    use_angle_cls: bool
    use_space_char: bool
    total_files: int
    processed_files: int
    successful_files: int
    failed_files: int
    progress: float
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    files: Optional[List[FileInfoSchema]] = None
    results: Optional[List[OCRResultSchema]] = None
    
    class Config:
        from_attributes = True


class TaskListSchema(BaseModel):
    """任务列表模型"""
    tasks: List[TaskSchema]
    total: int
    page: int
    page_size: int


class SingleRecognitionRequest(BaseModel):
    """单图片识别请求模型"""
    config: Optional[OCRConfigSchema] = None


class SingleRecognitionResponse(BaseModel):
    """单图片识别响应模型"""
    success: bool
    file_name: str
    text: str
    confidence: float
    word_count: int
    line_count: int
    details: List[Dict[str, Any]]
    processing_time: float
    error: Optional[str] = None


class BatchRecognitionRequest(BaseModel):
    """批量识别请求模型"""
    task_name: str
    task_description: Optional[str] = None
    priority: int = 2
    config: OCRConfigSchema


class BatchRecognitionResponse(BaseModel):
    """批量识别响应模型"""
    success: bool
    task_id: str
    message: str
    total_files: int


class TaskProgressResponse(BaseModel):
    """任务进度响应模型"""
    task_id: str
    status: str
    progress: float
    processed_files: int
    total_files: int
    successful_files: int
    failed_files: int
    current_file: Optional[str] = None
    estimated_remaining_time: Optional[int] = None


class ResultUpdateRequest(BaseModel):
    """结果更新请求模型"""
    text_content: str


class ResultUpdateResponse(BaseModel):
    """结果更新响应模型"""
    success: bool
    message: str
    result: OCRResultSchema


class ExportRequest(BaseModel):
    """导出请求模型"""
    format: str = Field(..., description="导出格式")
    task_ids: Optional[List[str]] = Field(None, description="任务ID列表")
    result_ids: Optional[List[int]] = Field(None, description="结果ID列表")
    include_metadata: bool = Field(default=True, description="是否包含元数据")


class ExportResponse(BaseModel):
    """导出响应模型"""
    success: bool
    export_id: str
    download_url: str
    filename: str
    file_size: int
    expires_at: datetime


class SystemStatsResponse(BaseModel):
    """系统统计响应模型"""
    task_stats: Dict[str, int]
    file_stats: Dict[str, int]
    processing_stats: Dict[str, float]
    recent_activity: List[Dict[str, Any]]


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class SuccessResponse(BaseModel):
    """成功响应模型"""
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    timestamp: datetime
    version: str
    uptime: float
    database_status: str
    ocr_engine_status: str
