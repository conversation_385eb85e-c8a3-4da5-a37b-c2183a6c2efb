"""
识别结果管理相关的API路由
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.api.schemas import (
    OCRResultSchema, ResultUpdateRequest, ResultUpdateResponse,
    ExportRequest, ExportResponse
)
from app.database.database import get_db
from app.database.crud import ResultCRUD, ExportCRUD
from loguru import logger

router = APIRouter(prefix="/api/results", tags=["Results"])


@router.get("/", response_model=List[OCRResultSchema])
async def get_results(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(default=None, description="搜索关键词"),
    task_id: Optional[str] = Query(default=None, description="任务ID筛选"),
    start_date: Optional[datetime] = Query(default=None, description="开始日期"),
    end_date: Optional[datetime] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """获取识别结果列表"""
    try:
        skip = (page - 1) * page_size
        
        if search:
            # 搜索结果
            results = ResultCRUD.search_results(db, search, skip=skip, limit=page_size)
        elif start_date and end_date:
            # 按日期范围查询
            results = ResultCRUD.get_results_by_date_range(db, start_date, end_date)
            results = results[skip:skip + page_size]  # 手动分页
        elif task_id:
            # 按任务ID查询
            results = ResultCRUD.get_results_by_task(db, task_id)
            results = results[skip:skip + page_size]  # 手动分页
        else:
            # 获取所有结果（需要实现分页查询）
            # 这里简化实现，实际应该在CRUD中添加通用的分页查询方法
            all_results = db.query(ResultCRUD.get_result.__annotations__['return'].__args__[0]).all()
            results = all_results[skip:skip + page_size]
        
        return [OCRResultSchema.from_orm(result) for result in results]
    
    except Exception as e:
        logger.error(f"获取结果列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取结果列表失败: {str(e)}")


@router.get("/{result_id}", response_model=OCRResultSchema)
async def get_result(result_id: int, db: Session = Depends(get_db)):
    """获取单个识别结果"""
    try:
        result = ResultCRUD.get_result(db, result_id)
        if not result:
            raise HTTPException(status_code=404, detail="结果不存在")
        
        return OCRResultSchema.from_orm(result)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取结果详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取结果详情失败: {str(e)}")


@router.put("/{result_id}", response_model=ResultUpdateResponse)
async def update_result(
    result_id: int,
    update_data: ResultUpdateRequest,
    db: Session = Depends(get_db)
):
    """更新识别结果"""
    try:
        # 检查结果是否存在
        existing_result = ResultCRUD.get_result(db, result_id)
        if not existing_result:
            raise HTTPException(status_code=404, detail="结果不存在")
        
        # 更新结果
        updated_result = ResultCRUD.update_result(
            db, 
            result_id, 
            {"text_content": update_data.text_content}
        )
        
        if updated_result:
            return ResultUpdateResponse(
                success=True,
                message="结果更新成功",
                result=OCRResultSchema.from_orm(updated_result)
            )
        else:
            raise HTTPException(status_code=400, detail="结果更新失败")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新结果失败: {str(e)}")


@router.delete("/{result_id}")
async def delete_result(result_id: int, db: Session = Depends(get_db)):
    """删除识别结果"""
    try:
        # 检查结果是否存在
        existing_result = ResultCRUD.get_result(db, result_id)
        if not existing_result:
            raise HTTPException(status_code=404, detail="结果不存在")
        
        # 删除结果（这里需要在CRUD中实现delete方法）
        # success = ResultCRUD.delete_result(db, result_id)
        
        # 临时实现
        db.delete(existing_result)
        db.commit()
        
        return {"success": True, "message": "结果已删除"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除结果失败: {str(e)}")


@router.post("/search")
async def search_results(
    query: str = Query(..., description="搜索关键词"),
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """搜索识别结果"""
    try:
        skip = (page - 1) * page_size
        results = ResultCRUD.search_results(db, query, skip=skip, limit=page_size)
        
        return {
            "success": True,
            "query": query,
            "total_results": len(results),
            "page": page,
            "page_size": page_size,
            "results": [OCRResultSchema.from_orm(result) for result in results]
        }
    
    except Exception as e:
        logger.error(f"搜索结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.post("/export", response_model=ExportResponse)
async def export_results(
    export_request: ExportRequest,
    db: Session = Depends(get_db)
):
    """导出识别结果"""
    try:
        import uuid
        from pathlib import Path
        
        # 生成导出ID
        export_id = str(uuid.uuid4())
        
        # 获取要导出的结果
        results_to_export = []
        
        if export_request.result_ids:
            # 按结果ID导出
            for result_id in export_request.result_ids:
                result = ResultCRUD.get_result(db, result_id)
                if result:
                    results_to_export.append(result)
        
        elif export_request.task_ids:
            # 按任务ID导出
            for task_id in export_request.task_ids:
                task_results = ResultCRUD.get_results_by_task(db, task_id)
                results_to_export.extend(task_results)
        
        if not results_to_export:
            raise HTTPException(status_code=400, detail="没有找到要导出的结果")
        
        # 生成导出文件
        export_filename = f"ocr_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_request.format}"
        export_path = Path("./results/exports") / export_filename
        export_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 根据格式生成文件内容
        if export_request.format == "txt":
            content = _generate_txt_export(results_to_export, export_request.include_metadata)
        elif export_request.format == "json":
            content = _generate_json_export(results_to_export, export_request.include_metadata)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的导出格式: {export_request.format}")
        
        # 写入文件
        with open(export_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        file_size = export_path.stat().st_size
        
        # 创建导出记录
        export_data = {
            "export_id": export_id,
            "export_type": "batch" if len(results_to_export) > 1 else "single",
            "format": export_request.format,
            "task_ids": export_request.task_ids or [],
            "result_ids": export_request.result_ids or [],
            "filename": export_filename,
            "file_path": str(export_path),
            "file_size": file_size,
            "total_results": len(results_to_export),
            "total_text_length": sum(len(r.text_content or "") for r in results_to_export),
            "expires_at": datetime.now() + timedelta(days=7)  # 7天后过期
        }
        
        ExportCRUD.create_export_record(db, export_data)
        
        return ExportResponse(
            success=True,
            export_id=export_id,
            download_url=f"/api/exports/{export_id}/download",
            filename=export_filename,
            file_size=file_size,
            expires_at=export_data["expires_at"]
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


def _generate_txt_export(results: List, include_metadata: bool = True) -> str:
    """生成TXT格式的导出内容"""
    content = []
    
    if include_metadata:
        content.append("OCR识别结果导出")
        content.append(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append(f"结果数量: {len(results)}")
        content.append("=" * 50)
        content.append("")
    
    for i, result in enumerate(results, 1):
        if include_metadata:
            content.append(f"结果 {i}:")
            content.append(f"任务ID: {result.task_id}")
            content.append(f"置信度: {result.confidence:.2%}")
            content.append(f"字符数: {result.word_count}")
            content.append(f"行数: {result.line_count}")
            content.append(f"识别时间: {result.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("-" * 30)
        
        content.append(result.text_content or "")
        content.append("")
        
        if include_metadata:
            content.append("=" * 50)
            content.append("")
    
    return "\n".join(content)


def _generate_json_export(results: List, include_metadata: bool = True) -> str:
    """生成JSON格式的导出内容"""
    import json
    
    export_data = {
        "export_info": {
            "export_time": datetime.now().isoformat(),
            "total_results": len(results),
            "format": "json"
        } if include_metadata else {},
        "results": []
    }
    
    for result in results:
        result_data = {
            "text_content": result.text_content,
            "confidence": result.confidence,
            "word_count": result.word_count,
            "line_count": result.line_count
        }
        
        if include_metadata:
            result_data.update({
                "task_id": result.task_id,
                "file_id": result.file_id,
                "processing_time": result.processing_time,
                "success": result.success,
                "is_edited": result.is_edited,
                "created_at": result.created_at.isoformat(),
                "updated_at": result.updated_at.isoformat()
            })
        
        export_data["results"].append(result_data)
    
    return json.dumps(export_data, ensure_ascii=False, indent=2)
