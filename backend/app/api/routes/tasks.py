"""
任务管理相关的API路由
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session

from app.api.schemas import TaskSchema, TaskListSchema, TaskProgressResponse
from app.database.database import get_db
from app.database.crud import TaskCRUD, FileCRUD, ResultCRUD
from app.core.task_manager import get_task_manager
from loguru import logger

router = APIRouter(prefix="/api/tasks", tags=["Tasks"])


@router.get("/", response_model=TaskListSchema)
async def get_tasks(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(default=None, description="任务状态筛选"),
    db: Session = Depends(get_db)
):
    """获取任务列表"""
    try:
        skip = (page - 1) * page_size
        
        # 获取任务列表
        tasks = TaskCRUD.get_tasks(db, skip=skip, limit=page_size, status=status)
        
        # 获取总数（简化实现，实际应该根据筛选条件计算）
        total = len(TaskCRUD.get_tasks(db, skip=0, limit=1000, status=status))
        
        # 转换为响应模型
        task_schemas = []
        for task in tasks:
            task_schema = TaskSchema.from_orm(task)
            task_schemas.append(task_schema)
        
        return TaskListSchema(
            tasks=task_schemas,
            total=total,
            page=page,
            page_size=page_size
        )
    
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.get("/{task_id}", response_model=TaskSchema)
async def get_task(task_id: str, db: Session = Depends(get_db)):
    """获取单个任务详情"""
    try:
        # 从数据库获取任务
        db_task = TaskCRUD.get_task(db, task_id)
        if not db_task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 获取任务的文件列表
        files = FileCRUD.get_files_by_task(db, task_id)
        
        # 获取任务的结果列表
        results = ResultCRUD.get_results_by_task(db, task_id)
        
        # 转换为响应模型
        task_schema = TaskSchema.from_orm(db_task)
        task_schema.files = [file for file in files] if files else []
        task_schema.results = [result for result in results] if results else []
        
        return task_schema
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务详情失败: {str(e)}")


@router.get("/{task_id}/progress", response_model=TaskProgressResponse)
async def get_task_progress(task_id: str, db: Session = Depends(get_db)):
    """获取任务进度"""
    try:
        # 从数据库获取任务信息
        db_task = TaskCRUD.get_task(db, task_id)
        if not db_task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 从任务管理器获取实时进度
        task_manager = get_task_manager()
        task = task_manager.get_task(task_id)
        
        if task:
            # 使用任务管理器中的实时数据
            return TaskProgressResponse(
                task_id=task_id,
                status=task.status.value,
                progress=task.progress,
                processed_files=task.processed_files,
                total_files=task.total_files,
                successful_files=task.successful_files,
                failed_files=task.failed_files
            )
        else:
            # 使用数据库中的数据
            return TaskProgressResponse(
                task_id=task_id,
                status=db_task.status,
                progress=db_task.progress,
                processed_files=db_task.processed_files,
                total_files=db_task.total_files,
                successful_files=db_task.successful_files,
                failed_files=db_task.failed_files
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务进度失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取进度失败: {str(e)}")


@router.post("/{task_id}/cancel")
async def cancel_task(task_id: str, db: Session = Depends(get_db)):
    """取消任务"""
    try:
        # 检查任务是否存在
        db_task = TaskCRUD.get_task(db, task_id)
        if not db_task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 检查任务状态
        if db_task.status in ["completed", "failed", "cancelled"]:
            raise HTTPException(status_code=400, detail="任务已完成或已取消，无法取消")
        
        # 取消任务
        task_manager = get_task_manager()
        success = task_manager.cancel_task(task_id)
        
        if success:
            # 更新数据库状态
            TaskCRUD.update_task(db, task_id, {"status": "cancelled"})
            return {"success": True, "message": "任务已取消"}
        else:
            raise HTTPException(status_code=400, detail="任务取消失败")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.delete("/{task_id}")
async def delete_task(task_id: str, db: Session = Depends(get_db)):
    """删除任务"""
    try:
        # 检查任务是否存在
        db_task = TaskCRUD.get_task(db, task_id)
        if not db_task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 先取消任务（如果正在运行）
        task_manager = get_task_manager()
        task_manager.cancel_task(task_id)
        
        # 删除任务管理器中的任务
        task_manager.delete_task(task_id)
        
        # 删除数据库中的任务（级联删除相关文件和结果）
        success = TaskCRUD.delete_task(db, task_id)
        
        if success:
            return {"success": True, "message": "任务已删除"}
        else:
            raise HTTPException(status_code=400, detail="任务删除失败")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")


@router.get("/{task_id}/results")
async def get_task_results(task_id: str, db: Session = Depends(get_db)):
    """获取任务的所有识别结果"""
    try:
        # 检查任务是否存在
        db_task = TaskCRUD.get_task(db, task_id)
        if not db_task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 获取任务的结果
        results = ResultCRUD.get_results_by_task(db, task_id)
        
        return {
            "success": True,
            "task_id": task_id,
            "total_results": len(results),
            "results": results
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务结果失败: {str(e)}")


@router.get("/statistics/overview")
async def get_task_statistics(db: Session = Depends(get_db)):
    """获取任务统计信息"""
    try:
        stats = TaskCRUD.get_task_statistics(db)
        
        return {
            "success": True,
            "statistics": stats
        }
    
    except Exception as e:
        logger.error(f"获取任务统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
