"""
OCR相关的API路由
"""

import os
import time
from typing import List, Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.api.schemas import (
    SingleRecognitionRequest, SingleRecognitionResponse,
    BatchRecognitionRequest, BatchRecognitionResponse,
    TaskProgressResponse, OCRConfigSchema
)
from app.core.ocr_engine import get_ocr_engine
from app.core.file_handler import get_file_handler
from app.core.task_manager import get_task_manager, OCRTaskConfig, TaskPriority
from app.database.database import get_db
from app.database.crud import TaskCRUD, FileCRUD, ResultCRUD
from loguru import logger

router = APIRouter(prefix="/api/ocr", tags=["OCR"])


@router.post("/single", response_model=SingleRecognitionResponse)
async def single_recognition(
    file: UploadFile = File(...),
    language: str = Form(default="ch"),
    mode: str = Form(default="accurate"),
    use_angle_cls: bool = Form(default=True),
    db: Session = Depends(get_db)
):
    """单图片OCR识别"""
    try:
        start_time = time.time()
        
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 保存文件
        file_handler = get_file_handler()
        save_result = await file_handler.save_uploaded_file(
            file_content, file.filename, "temp"
        )
        
        if not save_result["success"]:
            raise HTTPException(status_code=400, detail=save_result["error"])
        
        # 执行OCR识别
        ocr_engine = get_ocr_engine()
        recognition_result = ocr_engine.recognize_single_image(save_result["file_path"])
        
        # 清理临时文件
        file_handler.delete_file(save_result["file_path"])
        
        processing_time = time.time() - start_time
        
        if recognition_result["success"]:
            return SingleRecognitionResponse(
                success=True,
                file_name=file.filename,
                text=recognition_result["text"],
                confidence=recognition_result["confidence"],
                word_count=recognition_result["word_count"],
                line_count=recognition_result["line_count"],
                details=recognition_result["details"],
                processing_time=processing_time
            )
        else:
            return SingleRecognitionResponse(
                success=False,
                file_name=file.filename,
                text="",
                confidence=0.0,
                word_count=0,
                line_count=0,
                details=[],
                processing_time=processing_time,
                error=recognition_result.get("error", "识别失败")
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"单图片识别失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")


@router.post("/batch", response_model=BatchRecognitionResponse)
async def batch_recognition(
    files: List[UploadFile] = File(...),
    task_name: str = Form(...),
    task_description: str = Form(default=""),
    priority: int = Form(default=2),
    language: str = Form(default="ch"),
    mode: str = Form(default="accurate"),
    output_format: str = Form(default="txt"),
    use_angle_cls: bool = Form(default=True),
    use_space_char: bool = Form(default=True),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """批量OCR识别"""
    try:
        # 验证文件
        if not files:
            raise HTTPException(status_code=400, detail="请上传至少一个文件")
        
        if len(files) > 100:  # 限制批量文件数量
            raise HTTPException(status_code=400, detail="批量文件数量不能超过100个")
        
        # 保存上传的文件
        file_handler = get_file_handler()
        saved_files = []
        
        for file in files:
            if not file.filename:
                continue
            
            file_content = await file.read()
            save_result = await file_handler.save_uploaded_file(
                file_content, file.filename, "batch"
            )
            
            if save_result["success"]:
                saved_files.append(save_result)
        
        if not saved_files:
            raise HTTPException(status_code=400, detail="没有有效的文件")
        
        # 创建OCR任务配置
        config = OCRTaskConfig(
            language=language,
            mode=mode,
            output_format=output_format,
            use_angle_cls=use_angle_cls,
            use_space_char=use_space_char
        )
        
        # 创建任务
        task_manager = get_task_manager()
        file_paths = [f["file_path"] for f in saved_files]
        
        task_id = task_manager.create_task(
            name=task_name,
            description=task_description,
            file_paths=file_paths,
            config=config,
            priority=TaskPriority(priority)
        )
        
        # 在数据库中创建任务记录
        task_data = {
            "task_id": task_id,
            "name": task_name,
            "description": task_description,
            "status": "pending",
            "priority": priority,
            "language": language,
            "mode": mode,
            "output_format": output_format,
            "use_angle_cls": use_angle_cls,
            "use_space_char": use_space_char,
            "total_files": len(saved_files)
        }
        
        db_task = TaskCRUD.create_task(db, task_data)
        
        # 创建文件记录
        for file_info in saved_files:
            file_data = {
                "task_id": task_id,
                "original_filename": file_info["original_filename"],
                "stored_filename": file_info["filename"],
                "file_path": file_info["file_path"],
                "file_size": file_info["size"],
                "file_hash": file_info["hash"],
                "mime_type": file_info["mime_type"],
                "extension": file_info["extension"],
                "status": "pending"
            }
            FileCRUD.create_file(db, file_data)
        
        return BatchRecognitionResponse(
            success=True,
            task_id=task_id,
            message="批量识别任务创建成功",
            total_files=len(saved_files)
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量识别任务创建失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"任务创建失败: {str(e)}")


@router.get("/task/{task_id}/progress", response_model=TaskProgressResponse)
async def get_task_progress(task_id: str, db: Session = Depends(get_db)):
    """获取任务进度"""
    try:
        # 从数据库获取任务信息
        db_task = TaskCRUD.get_task(db, task_id)
        if not db_task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 从任务管理器获取实时进度
        task_manager = get_task_manager()
        task = task_manager.get_task(task_id)
        
        if task:
            # 使用任务管理器中的实时数据
            return TaskProgressResponse(
                task_id=task_id,
                status=task.status.value,
                progress=task.progress,
                processed_files=task.processed_files,
                total_files=task.total_files,
                successful_files=task.successful_files,
                failed_files=task.failed_files
            )
        else:
            # 使用数据库中的数据
            return TaskProgressResponse(
                task_id=task_id,
                status=db_task.status,
                progress=db_task.progress,
                processed_files=db_task.processed_files,
                total_files=db_task.total_files,
                successful_files=db_task.successful_files,
                failed_files=db_task.failed_files
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务进度失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取进度失败: {str(e)}")


@router.post("/task/{task_id}/cancel")
async def cancel_task(task_id: str, db: Session = Depends(get_db)):
    """取消任务"""
    try:
        # 检查任务是否存在
        db_task = TaskCRUD.get_task(db, task_id)
        if not db_task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 取消任务
        task_manager = get_task_manager()
        success = task_manager.cancel_task(task_id)
        
        if success:
            # 更新数据库状态
            TaskCRUD.update_task(db, task_id, {"status": "cancelled"})
            return {"success": True, "message": "任务已取消"}
        else:
            raise HTTPException(status_code=400, detail="任务取消失败")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/supported-formats")
async def get_supported_formats():
    """获取支持的文件格式"""
    try:
        ocr_engine = get_ocr_engine()
        formats = ocr_engine.get_supported_formats()
        
        return {
            "success": True,
            "formats": formats,
            "description": "支持的图片格式列表"
        }
    
    except Exception as e:
        logger.error(f"获取支持格式失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取支持格式失败: {str(e)}")
