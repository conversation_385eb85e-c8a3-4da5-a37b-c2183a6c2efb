"""
配置管理模块
"""

import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    APP_NAME: str = "OCR智能识别平台"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./ocr_platform.db"
    
    # Redis配置（用于任务队列）
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 文件存储配置
    UPLOAD_DIR: str = "./uploads"
    RESULTS_DIR: str = "./results"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".jpg", ".jpeg", ".png", ".bmp", ".webp", ".pdf"}
    
    # OCR配置
    OCR_USE_GPU: bool = False
    OCR_USE_ANGLE_CLS: bool = True
    OCR_USE_SPACE_CHAR: bool = True
    OCR_LANG: str = "ch"  # 默认中文
    
    # 任务配置
    MAX_BATCH_SIZE: int = 100
    TASK_TIMEOUT: int = 3600  # 1小时
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
