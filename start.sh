#!/bin/bash

# OCR智能识别平台服务管理脚本
# 使用方法: sh start.sh start|stop|restart|status

# 配置
BACKEND_DIR="backend"
FRONTEND_DIR="."  # 前端文件在根目录
BACKEND_PORT=8001
FRONTEND_PORT=3000
BACKEND_PID_FILE="backend.pid"
FRONTEND_PID_FILE="frontend.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 获取端口对应的进程ID
get_port_pid() {
    local port=$1
    lsof -Pi :$port -sTCP:LISTEN -t 2>/dev/null
}

# 检查模型缓存
check_models() {
    local model_cache_dir="$HOME/.paddlex/official_models"
    if [ -d "$model_cache_dir" ] && [ "$(ls -A $model_cache_dir 2>/dev/null | wc -l)" -gt 3 ]; then
        return 0  # 模型已缓存
    else
        return 1  # 模型未缓存
    fi
}

# 下载模型
download_models() {
    log_info "预下载PaddleOCR模型..."
    cd $BACKEND_DIR
    source venv/bin/activate
    python scripts/download_models.py
    local exit_code=$?
    cd ..
    return $exit_code
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."

    # 检查后端目录是否存在
    if [ ! -d "$BACKEND_DIR" ]; then
        log_error "后端目录 $BACKEND_DIR 不存在"
        return 1
    fi

    # 检查端口是否被占用
    if check_port $BACKEND_PORT; then
        local pid=$(get_port_pid $BACKEND_PORT)
        log_warning "后端端口 $BACKEND_PORT 已被占用 (PID: $pid)"
        return 1
    fi

    # 检查虚拟环境
    if [ ! -d "$BACKEND_DIR/venv" ]; then
        log_error "虚拟环境不存在，请先运行: cd $BACKEND_DIR && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
        return 1
    fi

    # 检查模型缓存
    if ! check_models; then
        log_warning "检测到模型未缓存，首次启动需要下载模型"
        log_info "您可以选择："
        log_info "1. 现在预下载模型（推荐）"
        log_info "2. 启动时自动下载（较慢）"
        echo -n "是否现在预下载模型？(Y/n): "
        read -r response
        case "$response" in
            [nN][oO]|[nN])
                log_info "跳过预下载，启动时将自动下载模型"
                ;;
            *)
                if download_models; then
                    log_success "模型预下载完成！"
                else
                    log_warning "模型预下载失败，启动时将自动下载"
                fi
                ;;
        esac
    else
        log_success "检测到模型缓存，启动将会很快"
    fi
    
    # 启动后端
    cd $BACKEND_DIR
    source venv/bin/activate
    nohup python -m uvicorn app.main:app --host 0.0.0.0 --port $BACKEND_PORT --reload > ../backend.log 2>&1 &
    local backend_pid=$!
    echo $backend_pid > ../$BACKEND_PID_FILE
    cd ..
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否启动成功
    if check_port $BACKEND_PORT; then
        log_success "后端服务启动成功 (PID: $backend_pid, Port: $BACKEND_PORT)"
        log_info "后端日志文件: backend.log"
        return 0
    else
        log_error "后端服务启动失败"
        return 1
    fi
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."

    # 检查前端主页文件是否存在
    if [ ! -f "index.html" ]; then
        log_error "前端主页文件 index.html 不存在"
        return 1
    fi
    
    # 检查端口是否被占用
    if check_port $FRONTEND_PORT; then
        local pid=$(get_port_pid $FRONTEND_PORT)
        log_warning "前端端口 $FRONTEND_PORT 已被占用 (PID: $pid)"
        return 1
    fi
    
    # 启动前端 (使用Python简单HTTP服务器)
    cd $FRONTEND_DIR
    nohup python -m http.server $FRONTEND_PORT > ../frontend.log 2>&1 &
    local frontend_pid=$!
    echo $frontend_pid > ../$FRONTEND_PID_FILE
    cd ..
    
    # 等待服务启动
    sleep 2
    
    # 检查服务是否启动成功
    if check_port $FRONTEND_PORT; then
        log_success "前端服务启动成功 (PID: $frontend_pid, Port: $FRONTEND_PORT)"
        log_info "前端日志文件: frontend.log"
        log_info "前端访问地址: http://localhost:$FRONTEND_PORT"
        return 0
    else
        log_error "前端服务启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    local service_name=$1
    local pid_file=$2
    local port=$3
    
    log_info "停止${service_name}服务..."
    
    # 从PID文件读取进程ID
    if [ -f "$pid_file" ]; then
        local pid=$(cat $pid_file)
        if ps -p $pid > /dev/null 2>&1; then
            kill $pid
            sleep 2
            if ps -p $pid > /dev/null 2>&1; then
                log_warning "强制停止${service_name}服务..."
                kill -9 $pid
            fi
            log_success "${service_name}服务已停止 (PID: $pid)"
        else
            log_warning "${service_name}服务进程不存在 (PID: $pid)"
        fi
        rm -f $pid_file
    fi
    
    # 检查端口是否还被占用
    if check_port $port; then
        local port_pid=$(get_port_pid $port)
        log_warning "端口 $port 仍被占用，强制停止进程 (PID: $port_pid)"
        kill -9 $port_pid 2>/dev/null
    fi
}

# 停止所有服务
stop_all() {
    stop_service "后端" $BACKEND_PID_FILE $BACKEND_PORT
    stop_service "前端" $FRONTEND_PID_FILE $FRONTEND_PORT
}

# 启动所有服务
start_all() {
    log_info "启动OCR智能识别平台..."
    echo "=================================="
    
    # 启动后端
    if start_backend; then
        echo ""
        # 启动前端
        if start_frontend; then
            echo ""
            log_success "OCR智能识别平台启动完成！"
            echo "=================================="
            log_info "后端API地址: http://localhost:$BACKEND_PORT"
            log_info "前端访问地址: http://localhost:$FRONTEND_PORT"
            log_info "健康检查: curl http://localhost:$BACKEND_PORT/health"
            echo "=================================="
        else
            log_error "前端启动失败，停止后端服务"
            stop_service "后端" $BACKEND_PID_FILE $BACKEND_PORT
            return 1
        fi
    else
        log_error "后端启动失败"
        return 1
    fi
}

# 重启所有服务
restart_all() {
    log_info "重启OCR智能识别平台..."
    stop_all
    sleep 2
    start_all
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    echo "=================================="
    
    # 检查后端状态
    if check_port $BACKEND_PORT; then
        local backend_pid=$(get_port_pid $BACKEND_PORT)
        log_success "后端服务运行中 (PID: $backend_pid, Port: $BACKEND_PORT)"
        
        # 健康检查
        if curl -s http://localhost:$BACKEND_PORT/health > /dev/null 2>&1; then
            log_success "后端健康检查通过"
        else
            log_warning "后端健康检查失败"
        fi
    else
        log_error "后端服务未运行"
    fi
    
    echo ""
    
    # 检查前端状态
    if check_port $FRONTEND_PORT; then
        local frontend_pid=$(get_port_pid $FRONTEND_PORT)
        log_success "前端服务运行中 (PID: $frontend_pid, Port: $FRONTEND_PORT)"
    else
        log_error "前端服务未运行"
    fi
    
    echo "=================================="
}

# 查看日志
show_logs() {
    local log_type=$1
    case "$log_type" in
        backend|back)
            log_info "查看后端日志 (Ctrl+C 退出)..."
            tail -f backend.log
            ;;
        frontend|front)
            log_info "查看前端日志 (Ctrl+C 退出)..."
            tail -f frontend.log
            ;;
        all)
            log_info "查看所有日志 (最近50行)..."
            echo "=== 后端日志 ==="
            tail -25 backend.log 2>/dev/null || echo "后端日志文件不存在"
            echo ""
            echo "=== 前端日志 ==="
            tail -25 frontend.log 2>/dev/null || echo "前端日志文件不存在"
            ;;
        *)
            log_error "无效的日志类型: $log_type"
            echo "使用方法: sh start.sh logs [backend|frontend|all]"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "OCR智能识别平台服务管理脚本"
    echo ""
    echo "使用方法:"
    echo "  sh start.sh start              - 启动所有服务"
    echo "  sh start.sh stop               - 停止所有服务"
    echo "  sh start.sh restart            - 重启所有服务"
    echo "  sh start.sh status             - 检查服务状态"
    echo "  sh start.sh logs [type]        - 查看日志"
    echo "  sh start.sh models             - 预下载OCR模型"
    echo "  sh start.sh help               - 显示帮助信息"
    echo ""
    echo "日志查看:"
    echo "  sh start.sh logs backend       - 实时查看后端日志"
    echo "  sh start.sh logs frontend      - 实时查看前端日志"
    echo "  sh start.sh logs all           - 查看所有日志摘要"
    echo ""
    echo "模型管理:"
    echo "  sh start.sh models             - 预下载OCR模型（推荐首次使用前执行）"
    echo ""
    echo "服务信息:"
    echo "  后端服务端口: $BACKEND_PORT"
    echo "  前端服务端口: $FRONTEND_PORT"
    echo "  后端日志文件: backend.log"
    echo "  前端日志文件: frontend.log"
    echo "  模型下载位置: ~/.paddlex/official_models"
    echo ""
    echo "💡 提示:"
    echo "  首次使用建议先运行 'sh start.sh models' 预下载模型"
    echo "  这样可以避免启动时等待1-2分钟的模型下载时间"
}

# 主函数
main() {
    case "$1" in
        start)
            start_all
            ;;
        stop)
            stop_all
            ;;
        restart)
            restart_all
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs "$2"
            ;;
        models)
            log_info "开始预下载OCR模型..."
            if download_models; then
                log_success "✅ 模型预下载完成！"
                log_info "现在启动服务将会很快，无需等待模型下载"
            else
                log_error "❌ 模型预下载失败！"
                exit 1
            fi
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的参数: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
