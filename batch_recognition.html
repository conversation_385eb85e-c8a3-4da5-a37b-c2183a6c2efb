<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量任务识别 - OCR智能识别</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
            min-height: 100vh;
        }
        .task-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
        }
        .task-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .progress-bar-bg {
            background: linear-gradient(90deg, #e5e7eb, #f3f4f6);
            border-radius: 10px;
        }
        .progress-bar-fill {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 10px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }
        .status-processing {
            color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        .status-completed {
            color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        .status-failed {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-nav {
            background: rgba(17, 24, 39, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        .upload-area {
            border: 3px dashed #cbd5e1;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        .upload-area.dragover {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
            transform: scale(1.02);
        }
        .upload-area:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #3b82f6;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 导航栏 -->
    <nav class="glass-nav text-white p-6 shadow-2xl">
        <div class="container mx-auto flex justify-between items-center">
            <a href="index.html" class="text-3xl font-bold flex items-center hover:scale-105 transition-transform">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-search-plus text-sky-600 text-xl"></i>
                </div>
                OCR智能识别
            </a>
            <div class="flex items-center space-x-2">
                <a href="index.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="single_recognition.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-file-image mr-2"></i>在线识别
                </a>
                <a href="results_management.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-chart-line mr-2"></i>结果管理
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="min-h-screen">
        <!-- 页面标题区域 -->
        <section class="py-8 px-6">
            <div class="max-w-6xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">批量图片识别任务</h2>
                <p class="text-lg text-blue-100 leading-relaxed mb-6 max-w-3xl mx-auto">
                    创建和管理您的批量图片文字识别任务，支持大量文件处理、进度跟踪和结果管理
                </p>
                <div class="flex flex-wrap justify-center items-center gap-6 text-blue-100 text-sm">
                    <span class="flex items-center">
                        <i class="fas fa-tasks mr-2 text-yellow-300"></i>
                        任务管理
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-chart-line mr-2 text-green-300"></i>
                        进度跟踪
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-edit mr-2 text-blue-300"></i>
                        结果编辑
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-download mr-2 text-purple-300"></i>
                        批量导出
                    </span>
                </div>
            </div>
        </section>

        <!-- 内容区域 -->
        <div class="px-6 pb-12">
            <div class="max-w-6xl mx-auto space-y-6">

                <!-- 创建新任务 -->
                <section class="glass-card p-6 rounded-2xl shadow-2xl">
                    <h3 class="text-xl font-bold mb-4 text-gray-800 flex items-center">
                        <div class="w-6 h-6 bg-gradient-to-r from-sky-500 to-blue-600 rounded-lg flex items-center justify-center mr-2">
                            <i class="fas fa-plus text-white text-sm"></i>
                        </div>
                        创建新识别任务
                    </h3>
                    <!-- 任务配置区域 -->
                    <div class="space-y-4">
                        <!-- 任务基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="taskName" class="block text-sm font-bold text-gray-700 mb-2">
                                    <i class="fas fa-tag mr-2 text-sky-500"></i>任务名称
                                </label>
                                <input type="text" id="taskName" name="taskName" placeholder="例如：扫描文档批次A" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-sm transition-all">
                            </div>
                            <div>
                                <label for="taskDescription" class="block text-sm font-bold text-gray-700 mb-2">
                                    <i class="fas fa-align-left mr-2 text-sky-500"></i>任务描述 (可选)
                                </label>
                                <input type="text" id="taskDescription" name="taskDescription" placeholder="简要描述此批次任务的内容" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-sm transition-all">
                            </div>
                        </div>

                        <!-- 识别设置 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="ocrLanguage" class="block text-sm font-bold text-gray-700 mb-2">
                                    <i class="fas fa-language mr-2 text-sky-500"></i>识别语言
                                </label>
                                <select id="ocrLanguage" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-sm transition-all">
                                    <option value="auto">自动检测</option>
                                    <option value="zh-cn">简体中文</option>
                                    <option value="zh-tw">繁体中文</option>
                                    <option value="en">英文</option>
                                    <option value="mixed">中英混合</option>
                                </select>
                            </div>
                            <div>
                                <label for="ocrMode" class="block text-sm font-bold text-gray-700 mb-2">
                                    <i class="fas fa-cogs mr-2 text-sky-500"></i>识别模式
                                </label>
                                <select id="ocrMode" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-sm transition-all">
                                    <option value="accurate">精确模式</option>
                                    <option value="fast">快速模式</option>
                                    <option value="handwriting">手写识别</option>
                                </select>
                            </div>
                            <div>
                                <label for="outputFormat" class="block text-sm font-bold text-gray-700 mb-2">
                                    <i class="fas fa-file-export mr-2 text-sky-500"></i>输出格式
                                </label>
                                <select id="outputFormat" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-sm transition-all">
                                    <option value="txt">纯文本 (.txt)</option>
                                    <option value="docx">Word文档 (.docx)</option>
                                    <option value="pdf">PDF文档 (.pdf)</option>
                                    <option value="excel">Excel表格 (.xlsx)</option>
                                </select>
                            </div>
                        </div>

                        <!-- 文件上传区域 -->
                        <div>
                            <label class="block text-sm font-bold text-gray-700 mb-2">
                                <i class="fas fa-cloud-upload-alt mr-2 text-sky-500"></i>上传文件
                            </label>
                            <div id="uploadArea" class="upload-area relative rounded-lg p-6 text-center cursor-pointer">
                                <input type="file" id="fileUploadBatch" name="fileUploadBatch" multiple webkitdirectory directory class="absolute inset-0 w-full h-full opacity-0 cursor-pointer">
                                <div id="uploadContent" class="pointer-events-none">
                                    <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-r from-sky-500 to-blue-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-cloud-upload-alt text-white text-lg"></i>
                                    </div>
                                    <h4 class="text-base font-semibold text-gray-700 mb-2">拖拽文件到此处或点击选择</h4>
                                    <p class="text-sm text-gray-500 mb-2">支持批量上传图片文件或选择文件夹</p>
                                    <div class="flex flex-wrap justify-center gap-2 text-xs text-gray-400">
                                        <span class="px-2 py-1 bg-gray-100 rounded">JPG</span>
                                        <span class="px-2 py-1 bg-gray-100 rounded">PNG</span>
                                        <span class="px-2 py-1 bg-gray-100 rounded">PDF</span>
                                        <span class="px-2 py-1 bg-gray-100 rounded">WEBP</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 flex justify-center gap-3">
                                <button type="button" id="selectFilesBtn" class="px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 rounded-lg transition-all flex items-center shadow-sm text-sm">
                                    <i class="fas fa-file mr-2"></i>选择文件
                                </button>
                                <button type="button" id="selectFolderBtn" class="px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 rounded-lg transition-all flex items-center shadow-sm text-sm">
                                    <i class="fas fa-folder mr-2"></i>选择文件夹
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 文件预览和创建按钮 -->
                    <div id="fileListPreview" class="mt-4 p-3 bg-gradient-to-r from-blue-50 to-sky-50 rounded-lg text-sm text-gray-700 hidden border border-blue-200"></div>
                    <div class="mt-4 text-center">
                        <button id="createTaskBtn" class="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white font-bold py-2.5 px-6 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-rocket mr-2"></i>创建任务
                        </button>
                    </div>
                </section>

                <!-- 任务列表 -->
                <section class="glass-card p-6 rounded-2xl shadow-2xl">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-gray-800 flex items-center">
                            <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-2">
                                <i class="fas fa-list text-white text-sm"></i>
                            </div>
                            任务列表
                        </h3>
                        <button id="refreshTasksBtn" class="px-3 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all flex items-center text-sm">
                            <i class="fas fa-sync-alt mr-1"></i>刷新列表
                        </button>
                    </div>
                    <div id="taskList" class="space-y-4">
                        <!-- 任务项将在此处动态添加 -->
                        <p id="noTasksText" class="text-gray-500 text-center py-12">
                            <i class="fas fa-tasks fa-3x mb-4 block"></i>
                            当前没有批量识别任务
                        </p>
                    </div>
                </section>
            </div>
        </div>

        <!-- 任务详情视图 -->
        <section id="taskDetailView" class="hidden min-h-screen px-6 pb-16">
            <div class="max-w-6xl mx-auto">
                <div class="glass-card p-8 rounded-2xl shadow-2xl">
                    <div class="flex justify-between items-center mb-6">
                        <h3 id="taskDetailName" class="text-2xl font-bold text-gray-800"></h3>
                        <button id="backToTaskListBtn" class="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all flex items-center">
                            <i class="fas fa-arrow-left mr-2"></i>返回任务列表
                        </button>
                    </div>
                    <div id="taskResultsContainer" class="space-y-6">
                        <!-- 识别结果将在此处动态添加 -->
                    </div>
                    <div class="mt-8 flex justify-end space-x-4">
                        <button id="saveCorrectionsBtn" class="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all flex items-center">
                            <i class="fas fa-save mr-2"></i>保存修改
                        </button>
                        <button id="exportResultsBtnDetail" class="px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-bold rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all flex items-center">
                            <i class="fas fa-download mr-2"></i>导出结果
                        </button>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg text-white text-center p-8">
        <div class="max-w-6xl mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-sky-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search-plus text-white"></i>
                    </div>
                    <span class="text-xl font-bold">OCR智能识别</span>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="#" class="hover:text-sky-400 transition-colors">使用条款</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">隐私政策</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">联系我们</a>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-6">
                <p class="text-gray-300">&copy; 2025 OCR智能识别平台-Design By XQJ. 保留所有权利.</p>
                <p class="text-sm text-gray-400 mt-2">技术支持：XQJ | 让AI为您的工作赋能</p>
            </div>
        </div>
    </footer>

    <script>
        const taskNameInput = document.getElementById('taskName');
        const taskDescriptionInput = document.getElementById('taskDescription');
        const ocrLanguageSelect = document.getElementById('ocrLanguage');
        const ocrModeSelect = document.getElementById('ocrMode');
        const outputFormatSelect = document.getElementById('outputFormat');
        const fileUploadBatch = document.getElementById('fileUploadBatch');
        const uploadArea = document.getElementById('uploadArea');
        const uploadContent = document.getElementById('uploadContent');
        const selectFilesBtn = document.getElementById('selectFilesBtn');
        const selectFolderBtn = document.getElementById('selectFolderBtn');
        const createTaskBtn = document.getElementById('createTaskBtn');
        const taskList = document.getElementById('taskList');
        const noTasksText = document.getElementById('noTasksText');
        const refreshTasksBtn = document.getElementById('refreshTasksBtn');
        const fileListPreview = document.getElementById('fileListPreview');
        const taskDetailView = document.getElementById('taskDetailView');
        const taskDetailName = document.getElementById('taskDetailName');
        const taskResultsContainer = document.getElementById('taskResultsContainer');
        const backToTaskListBtn = document.getElementById('backToTaskListBtn');
        const saveCorrectionsBtn = document.getElementById('saveCorrectionsBtn');
        const exportResultsBtnDetail = document.getElementById('exportResultsBtnDetail');


        let tasks = []; // 存储任务数据
        let selectedFilesForTask = [];
        let currentTaskInDetailView = null;

        // 文件上传相关事件处理
        function updateFilePreview() {
            if (selectedFilesForTask.length > 0) {
                const fileTypes = {};
                selectedFilesForTask.forEach(file => {
                    const ext = file.name.split('.').pop().toUpperCase();
                    fileTypes[ext] = (fileTypes[ext] || 0) + 1;
                });

                const typesList = Object.entries(fileTypes).map(([type, count]) => `${type}(${count})`).join(', ');
                fileListPreview.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="font-semibold">已选择 ${selectedFilesForTask.length} 个文件</span>
                        </div>
                        <button type="button" onclick="clearFiles()" class="text-red-500 hover:text-red-700 text-sm">
                            <i class="fas fa-times mr-1"></i>清除
                        </button>
                    </div>
                    <div class="mt-2 text-xs text-gray-600">文件类型: ${typesList}</div>
                `;
                fileListPreview.classList.remove('hidden');
                createTaskBtn.disabled = false;

                // 更新上传区域显示
                uploadContent.innerHTML = `
                    <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-white text-lg"></i>
                    </div>
                    <h4 class="text-base font-semibold text-green-700 mb-2">已选择 ${selectedFilesForTask.length} 个文件</h4>
                    <p class="text-sm text-gray-500">点击重新选择或拖拽添加更多文件</p>
                `;
            } else {
                fileListPreview.classList.add('hidden');
                createTaskBtn.disabled = true;

                // 恢复原始上传区域显示
                uploadContent.innerHTML = `
                    <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-r from-sky-500 to-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-cloud-upload-alt text-white text-lg"></i>
                    </div>
                    <h4 class="text-base font-semibold text-gray-700 mb-2">拖拽文件到此处或点击选择</h4>
                    <p class="text-sm text-gray-500 mb-2">支持批量上传图片文件或选择文件夹</p>
                    <div class="flex flex-wrap justify-center gap-2 text-xs text-gray-400">
                        <span class="px-2 py-1 bg-gray-100 rounded">JPG</span>
                        <span class="px-2 py-1 bg-gray-100 rounded">PNG</span>
                        <span class="px-2 py-1 bg-gray-100 rounded">PDF</span>
                        <span class="px-2 py-1 bg-gray-100 rounded">WEBP</span>
                    </div>
                `;
            }
        }

        window.clearFiles = function() {
            selectedFilesForTask = [];
            fileUploadBatch.value = '';
            updateFilePreview();
        }

        // 拖拽上传功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                selectedFilesForTask = files;
                updateFilePreview();
            }
        });

        // 文件选择事件
        fileUploadBatch.addEventListener('change', (event) => {
            selectedFilesForTask = Array.from(event.target.files);
            updateFilePreview();
        });

        // 选择文件按钮
        selectFilesBtn.addEventListener('click', () => {
            fileUploadBatch.removeAttribute('webkitdirectory');
            fileUploadBatch.removeAttribute('directory');
            fileUploadBatch.click();
        });

        // 选择文件夹按钮
        selectFolderBtn.addEventListener('click', () => {
            fileUploadBatch.setAttribute('webkitdirectory', '');
            fileUploadBatch.setAttribute('directory', '');
            fileUploadBatch.click();
        });

        createTaskBtn.addEventListener('click', () => {
            const taskName = taskNameInput.value.trim();
            if (!taskName) {
                alert('请输入任务名称！');
                taskNameInput.focus();
                return;
            }
            if (selectedFilesForTask.length === 0) {
                alert('请选择要识别的文件！');
                return;
            }

            const newTask = {
                id: `task-${Date.now()}`,
                name: taskName,
                description: taskDescriptionInput.value.trim(),
                language: ocrLanguageSelect.value,
                mode: ocrModeSelect.value,
                outputFormat: outputFormatSelect.value,
                files: selectedFilesForTask.map(f => f.name), // 只存储文件名以简化
                fileCount: selectedFilesForTask.length,
                status: 'processing', // 'processing', 'completed', 'failed'
                progress: 0, // 0 to 100
                createdAt: new Date().toLocaleString(),
                results: [] // 存储每个文件的识别结果
            };
            tasks.unshift(newTask); //添加到列表开头
            renderTaskList();
            simulateTaskProcessing(newTask.id);

            // Reset form
            taskNameInput.value = '';
            taskDescriptionInput.value = '';
            ocrLanguageSelect.value = 'auto';
            ocrModeSelect.value = 'accurate';
            outputFormatSelect.value = 'txt';
            clearFiles();
        });

        function simulateTaskProcessing(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task) return;

            let currentProgress = 0;
            const totalFiles = task.fileCount;
            let filesProcessed = 0;

            const interval = setInterval(() => {
                filesProcessed++;
                currentProgress = Math.round((filesProcessed / totalFiles) * 100);
                task.progress = currentProgress;
                
                // 模拟单个文件识别
                task.results.push({
                    fileName: task.files[filesProcessed-1] || `文件${filesProcessed}`,
                    text: `这是文件 '${task.files[filesProcessed-1] || `文件${filesProcessed}`}' 的模拟批量识别文本。\n时间: ${new Date().toLocaleTimeString()}`
                });

                if (filesProcessed >= totalFiles) {
                    clearInterval(interval);
                    task.status = 'completed';
                    task.progress = 100;
                }
                renderTaskList(); // Re-render to show progress
            }, 1000 + Math.random() * 1000); // Simulate variable processing time per file
        }

        function renderTaskList() {
            if (tasks.length === 0) {
                noTasksText.classList.remove('hidden');
                taskList.innerHTML = ''; // 清空列表以防万一
            } else {
                noTasksText.classList.add('hidden');
                taskList.innerHTML = ''; // Clear existing list items
                tasks.forEach(task => {
                    const taskElement = createTaskDOMElement(task);
                    taskList.appendChild(taskElement);
                });
            }
        }

        function createTaskDOMElement(task) {
            const div = document.createElement('div');
            div.className = 'task-item p-4 border border-gray-200 rounded-md shadow-sm';
            div.setAttribute('data-task-id', task.id);

            let statusColorClass = '';
            let statusIcon = '';
            let statusText = '';
            let viewDetailsButtonHTML = ''; // Placeholder, will be set based on status

            switch (task.status) {
                case 'processing':
                    statusColorClass = 'status-processing';
                    statusIcon = 'fas fa-spinner fa-spin';
                    statusText = '处理中';
                    break;
                case 'completed':
                    statusColorClass = 'status-completed';
                    statusIcon = 'fas fa-check-circle';
                    statusText = '已完成';
                    break;
                case 'failed':
                    statusColorClass = 'status-failed';
                    statusIcon = 'fas fa-times-circle';
                    statusText = '失败';
                    break;
            }
            // Add viewDetailsButtonHTML based on status
            if (task.status === 'completed') {
                 viewDetailsButtonHTML = `<button class="view-details-btn text-sm bg-purple-500 hover:bg-purple-600 text-white py-1 px-3 rounded-md mr-2" data-task-id="${task.id}"><i class="fas fa-search-plus mr-1"></i>查看并编辑详情</button>`;
            }

            // 获取语言和模式的显示文本
            const languageMap = {
                'auto': '自动检测',
                'zh-cn': '简体中文',
                'zh-tw': '繁体中文',
                'en': '英文',
                'mixed': '中英混合'
            };
            const modeMap = {
                'accurate': '精确模式',
                'fast': '快速模式',
                'handwriting': '手写识别'
            };
            const formatMap = {
                'txt': 'TXT',
                'docx': 'DOCX',
                'pdf': 'PDF',
                'excel': 'XLSX'
            };

            div.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-sky-700 mb-1">${task.name}</h4>
                        ${task.description ? `<p class="text-sm text-gray-600 mb-2">${task.description}</p>` : ''}
                        <div class="flex flex-wrap gap-2 text-xs">
                            <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">
                                <i class="fas fa-language mr-1"></i>${languageMap[task.language] || task.language}
                            </span>
                            <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full">
                                <i class="fas fa-cogs mr-1"></i>${modeMap[task.mode] || task.mode}
                            </span>
                            <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">
                                <i class="fas fa-file-export mr-1"></i>${formatMap[task.outputFormat] || task.outputFormat}
                            </span>
                        </div>
                    </div>
                    <span class="text-sm ${statusColorClass} ml-4"><i class="${statusIcon} mr-1"></i>${statusText}</span>
                </div>
                <div class="flex justify-between items-center text-sm text-gray-500 mb-3">
                    <span><i class="fas fa-images mr-1"></i>文件数量: ${task.fileCount}</span>
                    <span><i class="fas fa-clock mr-1"></i>${task.createdAt}</span>
                </div>
                ${task.status === 'processing' ? `
                <div class="mb-3">
                    <div class="progress-bar-bg w-full h-2.5 rounded-full">
                        <div class="progress-bar-fill h-2.5 rounded-full" style="width: ${task.progress}%;"></div>
                    </div>
                    <p class="text-xs text-right text-gray-500 mt-1">${task.progress}%</p>
                </div>
                ` : ''}
                <div class="flex justify-end gap-2">
                    ${viewDetailsButtonHTML}
                    ${task.status === 'completed' ? `
                        <button onclick="exportTaskResults('${task.id}')" class="text-sm bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white py-2 px-4 rounded-lg transition-all flex items-center">
                            <i class="fas fa-download mr-1"></i>导出结果
                        </button>
                    ` : ''}
                    ${task.status === 'failed' ? `
                        <button onclick="retryTask('${task.id}')" class="text-sm bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white py-2 px-4 rounded-lg transition-all flex items-center">
                            <i class="fas fa-redo mr-1"></i>重试
                        </button>
                    ` : ''}
                </div>
                <div id="results-preview-${task.id}" class="mt-3 p-3 bg-gray-50 border rounded-md max-h-60 overflow-y-auto hidden">
                    <h5 class="text-sm font-semibold mb-2 text-gray-700">识别结果预览:</h5>
                    <!-- 结果预览内容 -->
                </div>
            `;
            return div;
        }

        window.viewTaskResults = function(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task || task.status !== 'completed') {
                alert('任务未完成或不存在！');
                return;
            }
            const resultsPreviewDiv = document.getElementById(`results-preview-${taskId}`);
            if (!resultsPreviewDiv) return;

            if (resultsPreviewDiv.classList.contains('hidden') || resultsPreviewDiv.innerHTML.includes('<!-- 结果预览内容 -->')) {
                resultsPreviewDiv.innerHTML = '<h5 class="text-sm font-semibold mb-2 text-gray-700">识别结果预览:</h5>'; // Clear placeholder
                task.results.forEach(result => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'mb-2 pb-2 border-b border-gray-200 last:border-b-0';
                    itemDiv.innerHTML = `
                        <p class="font-medium text-xs text-sky-600">${result.fileName}</p>
                        <p class="text-xs text-gray-600 whitespace-pre-wrap">${result.text.substring(0,150)}${result.text.length > 150 ? '...' : ''}</p>
                    `;
                    resultsPreviewDiv.appendChild(itemDiv);
                });
                resultsPreviewDiv.classList.remove('hidden');
            } else {
                resultsPreviewDiv.classList.add('hidden');
            }
        }

        window.exportTaskResults = function(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task || task.status !== 'completed') {
                alert('任务未完成或不存在，无法导出！');
                return;
            }

            let fullText = `任务名称: ${task.name}\n`;
            fullText += `创建时间: ${task.createdAt}\n`;
            fullText += `文件总数: ${task.fileCount}\n`;
            fullText += `状态: ${task.status}\n\n`;
            fullText += `识别结果详情:\n====================================\n\n`;

            task.results.forEach(result => {
                fullText += `文件: ${result.fileName}\n`;
                fullText += `识别文本:\n${result.text}\n\n---\n\n`;
            });

            const blob = new Blob([fullText], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${task.name.replace(/\s+/g, '_')}_results.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        }
        
        window.retryTask = function(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task) return;
            task.status = 'processing';
            task.progress = 0;
            task.results = [];
            renderTaskList();
            simulateTaskProcessing(taskId);
        }

        refreshTasksBtn.addEventListener('click', () => {
            // In a real app, this would fetch from a server.
            // Here, we just re-render the current local `tasks` array.
            renderTaskList(); 
            // Potentially re-simulate if any are stuck in processing for demo
            tasks.forEach(task => {
                if (task.status === 'processing' && task.progress < 100) {
                    // This is a simplified re-trigger, a real app would need more robust state management
                    // For now, we won't auto-restart simulation on refresh to avoid complexity
                }
            });
        });

        // Initial render
        renderTaskList();

        function showTaskDetails(taskId) {
            currentTaskInDetailView = tasks.find(t => t.id === taskId);
            if (!currentTaskInDetailView) return;

            // Hide main sections, show detail view
            document.querySelector('main > div > div').classList.add('hidden'); // Main content area
            taskDetailView.classList.remove('hidden');

            taskDetailName.textContent = `任务详情: ${currentTaskInDetailView.name}`;
            taskResultsContainer.innerHTML = ''; // Clear previous results

            currentTaskInDetailView.results.forEach((result, index) => {
                const resultElement = document.createElement('div');
                resultElement.className = 'grid grid-cols-1 md:grid-cols-2 gap-6 p-4 border border-gray-200 rounded-lg shadow-sm';
                resultElement.innerHTML = `
                    <div>
                        <h5 class="font-semibold mb-2 text-gray-700"><i class="fas fa-image mr-2 text-sky-500"></i>${result.fileName} (原图)</h5>
                        <div class="w-full h-64 bg-gray-200 flex items-center justify-center text-gray-400 rounded-md border border-gray-300">
                            <i class="fas fa-camera fa-3x"></i>
                            <p class="ml-3 text-sm">原图预览区 (待实现)</p>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-semibold mb-2 text-gray-700"><i class="fas fa-file-alt mr-2 text-sky-500"></i>识别结果 (可编辑)</h5>
                        <textarea data-result-index="${index}" class="w-full h-64 p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm resize-none">${result.text}</textarea>
                    </div>
                `;
                taskResultsContainer.appendChild(resultElement);
            });
        }

        function hideTaskDetails() {
            taskDetailView.classList.add('hidden');
            document.querySelector('main > div > div').classList.remove('hidden');
            currentTaskInDetailView = null;
        }

        backToTaskListBtn.addEventListener('click', hideTaskDetails);

        saveCorrectionsBtn.addEventListener('click', () => {
            if (!currentTaskInDetailView) return;
            const textareas = taskResultsContainer.querySelectorAll('textarea[data-result-index]');
            textareas.forEach(textarea => {
                const index = parseInt(textarea.getAttribute('data-result-index'));
                currentTaskInDetailView.results[index].text = textarea.value;
            });
            alert('修改已成功保存到任务数据中！');
            // Note: This saves to the 'tasks' array in memory.
            // For persistence, you would send this data to a server.
        });

        exportResultsBtnDetail.addEventListener('click', () => {
            if (!currentTaskInDetailView) return;
            let exportContent = `任务名称: ${currentTaskInDetailView.name}\n`;
            exportContent += `创建时间: ${currentTaskInDetailView.createdAt}\n`;
            exportContent += `总文件数: ${currentTaskInDetailView.fileCount}\n\n`;
            exportContent += `详细识别结果:\n====================================\n\n`;

            currentTaskInDetailView.results.forEach(result => {
                exportContent += `文件: ${result.fileName}\n`;
                exportContent += `识别文本:\n${result.text}\n`;
                exportContent += `------------------------------------\n\n`;
            });

            const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${currentTaskInDetailView.name.replace(/\s+/g, '_')}_详细结果.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        });

        // Event delegation for view details buttons on the main task list
        taskList.addEventListener('click', (event) => {
            const viewDetailsButton = event.target.closest('.view-details-btn');
            if (viewDetailsButton) {
                const taskId = viewDetailsButton.getAttribute('data-task-id');
                showTaskDetails(taskId);
            }
        });

    </script>

</body>
</html>