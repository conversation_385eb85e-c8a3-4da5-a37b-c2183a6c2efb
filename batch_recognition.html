<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量任务识别 - OCR智能识别</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .task-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
        }
        .task-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .progress-bar-bg {
            background: linear-gradient(90deg, #e5e7eb, #f3f4f6);
            border-radius: 10px;
        }
        .progress-bar-fill {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 10px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }
        .status-processing {
            color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        .status-completed {
            color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        .status-failed {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-nav {
            background: rgba(14, 165, 233, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 导航栏 -->
    <nav class="glass-nav text-white p-6 shadow-2xl">
        <div class="container mx-auto flex justify-between items-center">
            <a href="index.html" class="text-3xl font-bold flex items-center hover:scale-105 transition-transform">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-search-plus text-sky-600 text-xl"></i>
                </div>
                OCR智能识别
            </a>
            <div class="flex items-center space-x-2">
                <a href="index.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="single_recognition.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-file-image mr-2"></i>在线识别
                </a>
                <a href="results_management.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-chart-line mr-2"></i>结果管理
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="container mx-auto mt-8 p-6">
        <header class="text-center mb-12">
            <div class="glass-card p-8 rounded-3xl max-w-4xl mx-auto">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">批量图片识别任务</h2>
                <p class="text-lg text-gray-600 leading-relaxed mb-6">
                    创建和管理您的批量图片文字识别任务，支持大量文件处理、进度跟踪和结果管理
                </p>
                <div class="flex flex-wrap justify-center items-center gap-6 text-sm text-gray-500">
                    <span class="flex items-center">
                        <i class="fas fa-tasks mr-2 text-purple-500"></i>
                        任务管理
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                        进度跟踪
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-edit mr-2 text-green-500"></i>
                        结果编辑
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-download mr-2 text-indigo-500"></i>
                        批量导出
                    </span>
                </div>
            </div>
        </header>

        <!-- 创建新任务 -->
        <section class="glass-card p-8 rounded-2xl shadow-2xl mb-8">
            <h3 class="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-plus text-white"></i>
                </div>
                创建新识别任务
            </h3>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 items-end">
                <div>
                    <label for="taskName" class="block text-sm font-bold text-gray-700 mb-2">任务名称</label>
                    <input type="text" id="taskName" name="taskName" placeholder="例如：扫描文档批次A" class="block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-sm transition-all">
                </div>
                <div>
                    <label for="fileUploadBatch" class="block text-sm font-bold text-gray-700 mb-2">上传文件 (可多选或文件夹)</label>
                    <input type="file" id="fileUploadBatch" name="fileUploadBatch" multiple webkitdirectory directory class="block w-full text-sm text-gray-500 file:mr-4 file:py-3 file:px-4 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-sky-50 file:text-sky-700 hover:file:bg-sky-100 border border-gray-300 rounded-xl">
                     <p class="text-xs text-gray-500 mt-2 flex items-center">
                        <i class="fas fa-info-circle mr-1 text-blue-500"></i>
                        选择文件夹时，将包含其内所有支持的图片文件
                     </p>
                </div>
                <button id="createTaskBtn" class="lg:mt-0 mt-6 w-full lg:w-auto bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-bold py-3 px-8 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105">
                    <i class="fas fa-rocket mr-2"></i>创建任务
                </button>
            </div>
            <div id="fileListPreview" class="mt-6 p-4 bg-gray-50 rounded-xl text-sm text-gray-600 hidden"></div>
        </section>

        <!-- 任务列表 -->
        <section class="bg-white p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-sky-600">任务列表</h3>
                <button id="refreshTasksBtn" class="text-sm text-sky-600 hover:text-sky-800">
                    <i class="fas fa-sync-alt mr-1"></i>刷新列表
                </button>
            </div>
            <div id="taskList" class="space-y-4">
                <!-- 任务项将在此处动态添加 -->
                <p id="noTasksText" class="text-gray-500 text-center py-8">当前没有批量识别任务。</p>
            </div>
        </section>

        <!-- 任务详情视图 -->
        <section id="taskDetailView" class="hidden bg-white p-6 rounded-lg shadow-lg mt-8">
            <div class="flex justify-between items-center mb-4">
                <h3 id="taskDetailName" class="text-2xl font-semibold text-sky-700"></h3>
                <button id="backToTaskListBtn" class="text-sm bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md">
                    <i class="fas fa-arrow-left mr-1"></i>返回任务列表
                </button>
            </div>
            <div id="taskResultsContainer" class="space-y-6">
                <!-- 识别结果将在此处动态添加 -->
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button id="saveCorrectionsBtn" class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md">
                    <i class="fas fa-save mr-1"></i>保存修改
                </button>
                <button id="exportResultsBtnDetail" class="bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-2 px-4 rounded-md">
                    <i class="fas fa-download mr-1"></i>导出结果
                </button>
            </div>
        </section>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white text-center p-6 mt-12">
        <p>&copy; 2024 OCR智能识别平台. 保留所有权利.</p>
    </footer>

    <script>
        const taskNameInput = document.getElementById('taskName');
        const fileUploadBatch = document.getElementById('fileUploadBatch');
        const createTaskBtn = document.getElementById('createTaskBtn');
        const taskList = document.getElementById('taskList');
        const noTasksText = document.getElementById('noTasksText');
        const refreshTasksBtn = document.getElementById('refreshTasksBtn');
        const fileListPreview = document.getElementById('fileListPreview');
        const taskDetailView = document.getElementById('taskDetailView');
        const taskDetailName = document.getElementById('taskDetailName');
        const taskResultsContainer = document.getElementById('taskResultsContainer');
        const backToTaskListBtn = document.getElementById('backToTaskListBtn');
        const saveCorrectionsBtn = document.getElementById('saveCorrectionsBtn');
        const exportResultsBtnDetail = document.getElementById('exportResultsBtnDetail');


        let tasks = []; // 存储任务数据
        let selectedFilesForTask = [];
        let currentTaskInDetailView = null;

        fileUploadBatch.addEventListener('change', (event) => {
            selectedFilesForTask = Array.from(event.target.files);
            if (selectedFilesForTask.length > 0) {
                fileListPreview.innerHTML = `已选择 ${selectedFilesForTask.length} 个文件/项目。`;
            } else {
                fileListPreview.innerHTML = '';
            }
        });

        createTaskBtn.addEventListener('click', () => {
            const taskName = taskNameInput.value.trim();
            if (!taskName) {
                alert('请输入任务名称！');
                taskNameInput.focus();
                return;
            }
            if (selectedFilesForTask.length === 0) {
                alert('请选择要识别的文件！');
                fileUploadBatch.click();
                return;
            }

            const newTask = {
                id: `task-${Date.now()}`,
                name: taskName,
                files: selectedFilesForTask.map(f => f.name), // 只存储文件名以简化
                fileCount: selectedFilesForTask.length,
                status: 'processing', // 'processing', 'completed', 'failed'
                progress: 0, // 0 to 100
                createdAt: new Date().toLocaleString(),
                results: [] // 存储每个文件的识别结果
            };
            tasks.unshift(newTask); //添加到列表开头
            renderTaskList();
            simulateTaskProcessing(newTask.id);

            // Reset form
            taskNameInput.value = '';
            fileUploadBatch.value = ''; // 清空文件选择
            selectedFilesForTask = [];
            fileListPreview.innerHTML = '';
        });

        function simulateTaskProcessing(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task) return;

            let currentProgress = 0;
            const totalFiles = task.fileCount;
            let filesProcessed = 0;

            const interval = setInterval(() => {
                filesProcessed++;
                currentProgress = Math.round((filesProcessed / totalFiles) * 100);
                task.progress = currentProgress;
                
                // 模拟单个文件识别
                task.results.push({
                    fileName: task.files[filesProcessed-1] || `文件${filesProcessed}`,
                    text: `这是文件 '${task.files[filesProcessed-1] || `文件${filesProcessed}`}' 的模拟批量识别文本。\n时间: ${new Date().toLocaleTimeString()}`
                });

                if (filesProcessed >= totalFiles) {
                    clearInterval(interval);
                    task.status = 'completed';
                    task.progress = 100;
                }
                renderTaskList(); // Re-render to show progress
            }, 1000 + Math.random() * 1000); // Simulate variable processing time per file
        }

        function renderTaskList() {
            if (tasks.length === 0) {
                noTasksText.classList.remove('hidden');
                taskList.innerHTML = ''; // 清空列表以防万一
            } else {
                noTasksText.classList.add('hidden');
                taskList.innerHTML = ''; // Clear existing list items
                tasks.forEach(task => {
                    const taskElement = createTaskDOMElement(task);
                    taskList.appendChild(taskElement);
                });
            }
        }

        function createTaskDOMElement(task) {
            const div = document.createElement('div');
            div.className = 'task-item p-4 border border-gray-200 rounded-md shadow-sm';
            div.setAttribute('data-task-id', task.id);

            let statusColorClass = '';
            let statusIcon = '';
            let statusText = '';
            let viewDetailsButtonHTML = ''; // Placeholder, will be set based on status

            switch (task.status) {
                case 'processing':
                    statusColorClass = 'status-processing';
                    statusIcon = 'fas fa-spinner fa-spin';
                    statusText = '处理中';
                    break;
                case 'completed':
                    statusColorClass = 'status-completed';
                    statusIcon = 'fas fa-check-circle';
                    statusText = '已完成';
                    break;
                case 'failed':
                    statusColorClass = 'status-failed';
                    statusIcon = 'fas fa-times-circle';
                    statusText = '失败';
                    break;
            }
            // Add viewDetailsButtonHTML based on status
            if (task.status === 'completed') {
                 viewDetailsButtonHTML = `<button class="view-details-btn text-sm bg-purple-500 hover:bg-purple-600 text-white py-1 px-3 rounded-md mr-2" data-task-id="${task.id}"><i class="fas fa-search-plus mr-1"></i>查看并编辑详情</button>`;
            }

            div.innerHTML = `
                <div class="flex justify-between items-center mb-2">
                    <h4 class="text-lg font-medium text-sky-700">${task.name}</h4>
                    <span class="text-sm ${statusColorClass}"><i class="${statusIcon} mr-1"></i>${statusText}</span>
                </div>
                <p class="text-sm text-gray-500">文件数量: ${task.fileCount} | 创建时间: ${task.createdAt}</p>
                ${task.status === 'processing' ? `
                <div class="mt-2">
                    <div class="progress-bar-bg w-full h-2.5 rounded-full">
                        <div class="progress-bar-fill h-2.5 rounded-full" style="width: ${task.progress}%;"></div>
                    </div>
                    <p class="text-xs text-right text-gray-500 mt-0.5">${task.progress}%</p>
                </div>
                ` : ''}
                <div class="mt-3 text-right">
                    ${viewDetailsButtonHTML} 
                    ${task.status === 'completed' ? `
                        <button onclick="exportTaskResults('${task.id}')" class="text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded-md"><i class="fas fa-file-download mr-1"></i>导出结果(汇总)</button>
                    ` : ''}
                    ${task.status === 'failed' ? `
                        <button onclick="retryTask('${task.id}')" class="text-sm bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-3 rounded-md mr-2"><i class="fas fa-redo mr-1"></i>重试</button>
                    ` : ''}
                </div>
                <div id="results-preview-${task.id}" class="mt-3 p-3 bg-gray-50 border rounded-md max-h-60 overflow-y-auto hidden">
                    <h5 class="text-sm font-semibold mb-2 text-gray-700">识别结果预览:</h5>
                    <!-- 结果预览内容 -->
                </div>
            `;
            return div;
        }

        window.viewTaskResults = function(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task || task.status !== 'completed') {
                alert('任务未完成或不存在！');
                return;
            }
            const resultsPreviewDiv = document.getElementById(`results-preview-${taskId}`);
            if (!resultsPreviewDiv) return;

            if (resultsPreviewDiv.classList.contains('hidden') || resultsPreviewDiv.innerHTML.includes('<!-- 结果预览内容 -->')) {
                resultsPreviewDiv.innerHTML = '<h5 class="text-sm font-semibold mb-2 text-gray-700">识别结果预览:</h5>'; // Clear placeholder
                task.results.forEach(result => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'mb-2 pb-2 border-b border-gray-200 last:border-b-0';
                    itemDiv.innerHTML = `
                        <p class="font-medium text-xs text-sky-600">${result.fileName}</p>
                        <p class="text-xs text-gray-600 whitespace-pre-wrap">${result.text.substring(0,150)}${result.text.length > 150 ? '...' : ''}</p>
                    `;
                    resultsPreviewDiv.appendChild(itemDiv);
                });
                resultsPreviewDiv.classList.remove('hidden');
            } else {
                resultsPreviewDiv.classList.add('hidden');
            }
        }

        window.exportTaskResults = function(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task || task.status !== 'completed') {
                alert('任务未完成或不存在，无法导出！');
                return;
            }

            let fullText = `任务名称: ${task.name}\n`;
            fullText += `创建时间: ${task.createdAt}\n`;
            fullText += `文件总数: ${task.fileCount}\n`;
            fullText += `状态: ${task.status}\n\n`;
            fullText += `识别结果详情:\n====================================\n\n`;

            task.results.forEach(result => {
                fullText += `文件: ${result.fileName}\n`;
                fullText += `识别文本:\n${result.text}\n\n---\n\n`;
            });

            const blob = new Blob([fullText], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${task.name.replace(/\s+/g, '_')}_results.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        }
        
        window.retryTask = function(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task) return;
            task.status = 'processing';
            task.progress = 0;
            task.results = [];
            renderTaskList();
            simulateTaskProcessing(taskId);
        }

        refreshTasksBtn.addEventListener('click', () => {
            // In a real app, this would fetch from a server.
            // Here, we just re-render the current local `tasks` array.
            renderTaskList(); 
            // Potentially re-simulate if any are stuck in processing for demo
            tasks.forEach(task => {
                if (task.status === 'processing' && task.progress < 100) {
                    // This is a simplified re-trigger, a real app would need more robust state management
                    // For now, we won't auto-restart simulation on refresh to avoid complexity
                }
            });
        });

        // Initial render
        renderTaskList();

        function showTaskDetails(taskId) {
            currentTaskInDetailView = tasks.find(t => t.id === taskId);
            if (!currentTaskInDetailView) return;

            // Hide main sections, show detail view
            document.querySelector('main > section.bg-white.p-6.rounded-lg.shadow-lg.mb-8').classList.add('hidden'); // Create task section
            document.getElementById('taskList').parentElement.classList.add('hidden'); // Task list section
            taskDetailView.classList.remove('hidden');

            taskDetailName.textContent = `任务详情: ${currentTaskInDetailView.name}`;
            taskResultsContainer.innerHTML = ''; // Clear previous results

            currentTaskInDetailView.results.forEach((result, index) => {
                const resultElement = document.createElement('div');
                resultElement.className = 'grid grid-cols-1 md:grid-cols-2 gap-6 p-4 border border-gray-200 rounded-lg shadow-sm';
                resultElement.innerHTML = `
                    <div>
                        <h5 class="font-semibold mb-2 text-gray-700"><i class="fas fa-image mr-2 text-sky-500"></i>${result.fileName} (原图)</h5>
                        <div class="w-full h-64 bg-gray-200 flex items-center justify-center text-gray-400 rounded-md border border-gray-300">
                            <i class="fas fa-camera fa-3x"></i>
                            <p class="ml-3 text-sm">原图预览区 (待实现)</p>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-semibold mb-2 text-gray-700"><i class="fas fa-file-alt mr-2 text-sky-500"></i>识别结果 (可编辑)</h5>
                        <textarea data-result-index="${index}" class="w-full h-64 p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm resize-none">${result.text}</textarea>
                    </div>
                `;
                taskResultsContainer.appendChild(resultElement);
            });
        }

        function hideTaskDetails() {
            taskDetailView.classList.add('hidden');
            document.querySelector('main > section.bg-white.p-6.rounded-lg.shadow-lg.mb-8').classList.remove('hidden');
            document.getElementById('taskList').parentElement.classList.remove('hidden');
            currentTaskInDetailView = null;
        }

        backToTaskListBtn.addEventListener('click', hideTaskDetails);

        saveCorrectionsBtn.addEventListener('click', () => {
            if (!currentTaskInDetailView) return;
            const textareas = taskResultsContainer.querySelectorAll('textarea[data-result-index]');
            textareas.forEach(textarea => {
                const index = parseInt(textarea.getAttribute('data-result-index'));
                currentTaskInDetailView.results[index].text = textarea.value;
            });
            alert('修改已成功保存到任务数据中！');
            // Note: This saves to the 'tasks' array in memory.
            // For persistence, you would send this data to a server.
        });

        exportResultsBtnDetail.addEventListener('click', () => {
            if (!currentTaskInDetailView) return;
            let exportContent = `任务名称: ${currentTaskInDetailView.name}\n`;
            exportContent += `创建时间: ${currentTaskInDetailView.createdAt}\n`;
            exportContent += `总文件数: ${currentTaskInDetailView.fileCount}\n\n`;
            exportContent += `详细识别结果:\n====================================\n\n`;

            currentTaskInDetailView.results.forEach(result => {
                exportContent += `文件: ${result.fileName}\n`;
                exportContent += `识别文本:\n${result.text}\n`;
                exportContent += `------------------------------------\n\n`;
            });

            const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${currentTaskInDetailView.name.replace(/\s+/g, '_')}_详细结果.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        });

        // Event delegation for view details buttons on the main task list
        taskList.addEventListener('click', (event) => {
            const viewDetailsButton = event.target.closest('.view-details-btn');
            if (viewDetailsButton) {
                const taskId = viewDetailsButton.getAttribute('data-task-id');
                showTaskDetails(taskId);
            }
        });

    </script>

</body>
</html>