<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端连接测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">前端连接测试</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">连接测试</h2>
            <button id="testConnection" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                测试后端连接
            </button>
            <div id="connectionResult" class="mt-4"></div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">OCR测试</h2>
            <input type="file" id="testFile" accept="image/*" class="mb-4">
            <div id="imageInfo" class="mb-4 text-sm text-gray-600"></div>
            <div class="flex gap-2 mb-4">
                <button id="testOCR" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    测试OCR识别
                </button>
                <button id="compressImage" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600" style="display:none;">
                    压缩图片后识别
                </button>
            </div>
            <div id="ocrResult" class="mt-4"></div>
        </div>
    </div>

    <script>
        // 测试后端连接
        document.getElementById('testConnection').addEventListener('click', async () => {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<p class="text-blue-500">正在测试连接...</p>';
            
            try {
                const response = await fetch('http://localhost:8001/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="text-green-600">
                            <p><strong>✅ 连接成功!</strong></p>
                            <p>状态: ${data.status}</p>
                            <p>版本: ${data.version}</p>
                            <p>运行时间: ${data.uptime}秒</p>
                            <p>OCR引擎状态: ${data.ocr_engine_status}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="text-red-600">❌ 连接失败: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="text-red-600">
                        <p><strong>❌ 连接错误:</strong></p>
                        <p>错误类型: ${error.name}</p>
                        <p>错误信息: ${error.message}</p>
                        <p class="mt-2 text-sm">
                            <strong>可能的原因:</strong><br>
                            1. 后端服务未启动<br>
                            2. 端口8001被占用<br>
                            3. CORS配置问题<br>
                            4. 网络连接问题
                        </p>
                    </div>
                `;
            }
        });

        // 文件选择处理
        document.getElementById('testFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const imageInfo = document.getElementById('imageInfo');
            const compressBtn = document.getElementById('compressImage');

            if (file) {
                const img = new Image();
                img.onload = function() {
                    const sizeKB = (file.size / 1024).toFixed(2);
                    const sizeMB = (file.size / (1024 * 1024)).toFixed(2);

                    imageInfo.innerHTML = `
                        <strong>图片信息:</strong> ${file.name}<br>
                        <strong>尺寸:</strong> ${img.width} x ${img.height} 像素<br>
                        <strong>大小:</strong> ${sizeKB} KB (${sizeMB} MB)
                    `;

                    // 如果图片太大，显示压缩按钮
                    if (img.width > 2048 || img.height > 2048 || file.size > 5 * 1024 * 1024) {
                        compressBtn.style.display = 'inline-block';
                        imageInfo.innerHTML += '<br><span class="text-orange-600">⚠️ 图片较大，建议压缩后识别</span>';
                    } else {
                        compressBtn.style.display = 'none';
                    }
                };
                img.src = URL.createObjectURL(file);
            } else {
                imageInfo.innerHTML = '';
                compressBtn.style.display = 'none';
            }
        });

        // 压缩图片函数
        function compressImage(file, maxWidth = 2048, maxHeight = 2048, quality = 0.8) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    // 计算新尺寸
                    let { width, height } = img;

                    if (width > maxWidth || height > maxHeight) {
                        const ratio = Math.min(maxWidth / width, maxHeight / height);
                        width *= ratio;
                        height *= ratio;
                    }

                    canvas.width = width;
                    canvas.height = height;

                    // 绘制压缩后的图片
                    ctx.drawImage(img, 0, 0, width, height);

                    // 转换为Blob
                    canvas.toBlob(resolve, 'image/jpeg', quality);
                };

                img.src = URL.createObjectURL(file);
            });
        }

        // 测试OCR识别
        async function performOCR(file, isCompressed = false) {
            const resultDiv = document.getElementById('ocrResult');

            resultDiv.innerHTML = `<p class="text-blue-500">正在识别图片${isCompressed ? '（压缩版）' : ''}...</p>`;

            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('language', 'ch');
                formData.append('mode', 'accurate');

                const response = await fetch('http://localhost:8001/api/ocr/single', {
                    method: 'POST',
                    body: formData,
                    signal: AbortSignal.timeout(60000) // 1分钟超时
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="text-green-600">
                                <p><strong>✅ 识别成功${isCompressed ? '（压缩版）' : ''}!</strong></p>
                                <p><strong>识别文本:</strong></p>
                                <div class="bg-gray-100 p-3 rounded mt-2">
                                    <pre class="whitespace-pre-wrap">${data.text}</pre>
                                </div>
                                <p class="mt-2"><strong>置信度:</strong> ${(data.confidence * 100).toFixed(2)}%</p>
                                <p><strong>字数:</strong> ${data.word_count}</p>
                                <p><strong>行数:</strong> ${data.line_count}</p>
                                <p><strong>处理时间:</strong> ${data.processing_time.toFixed(2)}秒</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<p class="text-red-600">❌ 识别失败: ${data.error}</p>`;
                    }
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<p class="text-red-600">❌ 服务器错误 (${response.status}): ${errorText}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="text-red-600">
                        <p><strong>❌ OCR识别错误:</strong></p>
                        <p>错误类型: ${error.name}</p>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }

        document.getElementById('testOCR').addEventListener('click', async () => {
            const fileInput = document.getElementById('testFile');

            if (!fileInput.files[0]) {
                document.getElementById('ocrResult').innerHTML = '<p class="text-red-600">请先选择一个图片文件</p>';
                return;
            }

            await performOCR(fileInput.files[0], false);
        });

        document.getElementById('compressImage').addEventListener('click', async () => {
            const fileInput = document.getElementById('testFile');

            if (!fileInput.files[0]) {
                document.getElementById('ocrResult').innerHTML = '<p class="text-red-600">请先选择一个图片文件</p>';
                return;
            }

            document.getElementById('ocrResult').innerHTML = '<p class="text-blue-500">正在压缩图片...</p>';

            try {
                const compressedFile = await compressImage(fileInput.files[0]);
                await performOCR(compressedFile, true);
            } catch (error) {
                document.getElementById('ocrResult').innerHTML = `<p class="text-red-600">❌ 图片压缩失败: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
