INFO:     Will watch for changes in these directories: ['/Users/<USER>/Documents/05Cursor/06OCR2/backend']
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:     Started reloader process [76616] using StatReload
/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
INFO:     Started server process [76619]
INFO:     Waiting for application startup.
2025-06-20 10:45:26 | INFO     | app.main:lifespan:57 - OCR智能识别平台启动中...
2025-06-20 10:45:26 | INFO     | app.main:lifespan:61 - 数据库初始化完成
2025-06-20 10:45:26 | INFO     | app.core.task_manager:load_tasks_from_disk:377 - 从磁盘加载了 0 个任务
2025-06-20 10:45:26 | INFO     | app.main:lifespan:68 - 任务处理器启动完成
2025-06-20 10:45:26 | INFO     | app.main:lifespan:70 - OCR智能识别平台启动完成
INFO:     Application startup complete.
2025-06-20 10:46:00 | INFO     | app.core.file_handler:save_uploaded_file:152 - 文件保存成功: ./uploads/temp/IMG_20250613_110305_20250620_104600.jpg
2025-06-20 10:46:00 | INFO     | app.core.ocr_engine:recognize_single_image:296 - 开始识别图片: ./uploads/temp/IMG_20250613_110305_20250620_104600.jpg
2025-06-20 10:46:00 | INFO     | app.core.ocr_engine:_preprocess_image:122 - 原始图片信息: 4096x1844, 文件大小: 1.34MB
2025-06-20 10:46:00 | INFO     | app.core.ocr_engine:_preprocess_image:157 - 图片智能压缩: 4096x1844 -> 1600x720 (压缩比: 0.39)
2025-06-20 10:46:00 | INFO     | app.core.ocr_engine:_get_ocr_instance:82 - 检测到模型缓存，快速初始化OCR引擎...
/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/paddle/utils/cpp_extension/extension_utils.py:711: UserWarning: No ccache found. Please be aware that recompiling all source files may be required. You can download and install ccache from: https://github.com/ccache/ccache/blob/master/doc/INSTALL.md
  warnings.warn(warning_message)
[32mCreating model: ('PP-LCNet_x1_0_doc_ori', None)[0m
[32mUsing official model (PP-LCNet_x1_0_doc_ori), the model files will be automatically downloaded and saved in /Users/<USER>/.paddlex/official_models.[0m

Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
Fetching 6 files: 100%|██████████| 6/6 [00:00<00:00, 6712.68it/s]
[32mCreating model: ('UVDoc', None)[0m
[32mUsing official model (UVDoc), the model files will be automatically downloaded and saved in /Users/<USER>/.paddlex/official_models.[0m

Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
Fetching 6 files: 100%|██████████| 6/6 [00:00<00:00, 3853.88it/s]
[32mCreating model: ('PP-LCNet_x1_0_textline_ori', None)[0m
[32mUsing official model (PP-LCNet_x1_0_textline_ori), the model files will be automatically downloaded and saved in /Users/<USER>/.paddlex/official_models.[0m
[32mCreating model: ('PP-OCRv5_server_det', None)[0m
[32mUsing official model (PP-OCRv5_server_det), the model files will be automatically downloaded and saved in /Users/<USER>/.paddlex/official_models.[0m

Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
Fetching 6 files: 100%|██████████| 6/6 [00:00<00:00, 4129.61it/s]
[32mCreating model: ('PP-OCRv5_server_rec', None)[0m
[32mUsing official model (PP-OCRv5_server_rec), the model files will be automatically downloaded and saved in /Users/<USER>/.paddlex/official_models.[0m

Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
Fetching 6 files: 100%|██████████| 6/6 [00:00<00:00, 1833.58it/s]
2025-06-20 10:46:06 | INFO     | app.core.ocr_engine:_get_ocr_instance:101 - OCR引擎初始化完成，耗时: 5.49秒
2025-06-20 10:46:06 | INFO     | app.core.ocr_engine:recognize_single_image:313 - 设置OCR超时时间: 15秒 (文件大小: 1.34MB)
2025-06-20 10:46:13 | INFO     | app.core.ocr_engine:recognize_single_image:325 - 图片识别完成: ./uploads/temp/IMG_20250613_110305_20250620_104600.jpg, 识别到 18 行文本
2025-06-20 10:46:13 | INFO     | app.core.file_handler:delete_file:242 - 文件删除成功: uploads/temp/IMG_20250613_110305_20250620_104600.jpg
INFO:     127.0.0.1:65242 - "POST /api/ocr/single HTTP/1.1" 200 OK
2025-06-20 10:51:16 | INFO     | app.core.file_handler:save_uploaded_file:152 - 文件保存成功: ./uploads/temp/20250620-1041072x.png
2025-06-20 10:51:16 | INFO     | app.core.ocr_engine:recognize_single_image:296 - 开始识别图片: ./uploads/temp/20250620-1041072x.png
2025-06-20 10:51:16 | INFO     | app.core.ocr_engine:_preprocess_image:122 - 原始图片信息: 1290x406, 文件大小: 0.73MB
2025-06-20 10:51:16 | INFO     | app.core.ocr_engine:_preprocess_image:159 - 图片尺寸适中，无需压缩: 1290x406
2025-06-20 10:51:16 | INFO     | app.core.ocr_engine:recognize_single_image:313 - 设置OCR超时时间: 15秒 (文件大小: 0.73MB)
2025-06-20 10:51:19 | INFO     | app.core.ocr_engine:recognize_single_image:325 - 图片识别完成: ./uploads/temp/20250620-1041072x.png, 识别到 14 行文本
2025-06-20 10:51:19 | INFO     | app.core.file_handler:delete_file:242 - 文件删除成功: uploads/temp/20250620-1041072x.png
INFO:     127.0.0.1:49229 - "POST /api/ocr/single HTTP/1.1" 200 OK
WARNING:  StatReload detected changes in 'app/core/ocr_engine.py'. Reloading...
/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
INFO:     Started server process [76741]
INFO:     Waiting for application startup.
2025-06-20 10:51:24 | INFO     | app.main:lifespan:57 - OCR智能识别平台启动中...
2025-06-20 10:51:24 | INFO     | app.main:lifespan:61 - 数据库初始化完成
2025-06-20 10:51:24 | INFO     | app.core.task_manager:load_tasks_from_disk:377 - 从磁盘加载了 0 个任务
2025-06-20 10:51:24 | INFO     | app.main:lifespan:68 - 任务处理器启动完成
2025-06-20 10:51:24 | INFO     | app.main:lifespan:70 - OCR智能识别平台启动完成
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'app/core/ocr_engine.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-20 10:52:15 | INFO     | app.main:lifespan:75 - OCR智能识别平台关闭中...
2025-06-20 10:52:15 | INFO     | app.main:lifespan:84 - OCR智能识别平台已关闭
INFO:     Application shutdown complete.
INFO:     Finished server process [76741]
/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
INFO:     Started server process [76753]
INFO:     Waiting for application startup.
2025-06-20 10:52:17 | INFO     | app.main:lifespan:57 - OCR智能识别平台启动中...
2025-06-20 10:52:17 | INFO     | app.main:lifespan:61 - 数据库初始化完成
2025-06-20 10:52:17 | INFO     | app.core.task_manager:load_tasks_from_disk:377 - 从磁盘加载了 0 个任务
2025-06-20 10:52:17 | INFO     | app.main:lifespan:68 - 任务处理器启动完成
2025-06-20 10:52:17 | INFO     | app.main:lifespan:70 - OCR智能识别平台启动完成
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'app/api/routes/ocr.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-20 10:52:56 | INFO     | app.main:lifespan:75 - OCR智能识别平台关闭中...
2025-06-20 10:52:56 | INFO     | app.main:lifespan:84 - OCR智能识别平台已关闭
INFO:     Application shutdown complete.
INFO:     Finished server process [76753]
/Users/<USER>/Documents/05Cursor/06OCR2/backend/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
INFO:     Started server process [76767]
INFO:     Waiting for application startup.
2025-06-20 10:52:58 | INFO     | app.main:lifespan:57 - OCR智能识别平台启动中...
2025-06-20 10:52:58 | INFO     | app.main:lifespan:61 - 数据库初始化完成
2025-06-20 10:52:58 | INFO     | app.core.task_manager:load_tasks_from_disk:377 - 从磁盘加载了 0 个任务
2025-06-20 10:52:58 | INFO     | app.main:lifespan:68 - 任务处理器启动完成
2025-06-20 10:52:58 | INFO     | app.main:lifespan:70 - OCR智能识别平台启动完成
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-20 10:55:15 | INFO     | app.main:lifespan:75 - OCR智能识别平台关闭中...
2025-06-20 10:55:15 | INFO     | app.main:lifespan:84 - OCR智能识别平台已关闭
INFO:     Application shutdown complete.
INFO:     Finished server process [76767]
INFO:     Stopping reloader process [76616]
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
