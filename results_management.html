<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结果管理 - OCR智能识别</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
            min-height: 100vh;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-nav {
            background: rgba(14, 165, 233, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .result-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .result-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        .hero-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 导航栏 -->
    <nav class="glass-nav text-white p-6 shadow-2xl">
        <div class="container mx-auto flex justify-between items-center">
            <a href="index.html" class="text-3xl font-bold flex items-center hover:scale-105 transition-transform">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-search-plus text-sky-600 text-xl"></i>
                </div>
                OCR智能识别
            </a>
            <div class="flex items-center space-x-2">
                <a href="index.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="single_recognition.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-file-image mr-2"></i>在线识别
                </a>
                <a href="batch_recognition.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-folder-open mr-2"></i>批量任务
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="container mx-auto mt-8 p-6">
        <!-- 页面标题 -->
        <header class="text-center mb-12">
            <div class="hero-section p-8 rounded-3xl max-w-4xl mx-auto">
                <h2 class="text-4xl font-bold text-white mb-4">识别结果管理</h2>
                <p class="text-lg text-blue-100 leading-relaxed mb-6">
                    查看、编辑和管理您的所有OCR识别结果，支持搜索、分类和批量操作
                </p>
                <div class="flex flex-wrap justify-center items-center gap-6 text-sm text-blue-100">
                    <span class="flex items-center">
                        <i class="fas fa-search mr-2 text-yellow-300"></i>
                        智能搜索
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-tags mr-2 text-green-300"></i>
                        分类管理
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-edit mr-2 text-blue-300"></i>
                        在线编辑
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-share-alt mr-2 text-purple-300"></i>
                        批量操作
                    </span>
                </div>
            </div>
        </header>

        <!-- 搜索和筛选工具栏 -->
        <section class="glass-card p-6 rounded-2xl shadow-2xl mb-8">
            <div class="flex flex-col lg:flex-row gap-4 items-center">
                <div class="flex-1 relative">
                    <input type="text" id="searchInput" placeholder="搜索识别结果..." class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500">
                    <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <div class="flex gap-3">
                    <select id="categoryFilter" class="px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-sky-500">
                        <option value="">所有分类</option>
                        <option value="document">文档</option>
                        <option value="receipt">票据</option>
                        <option value="card">证件</option>
                        <option value="other">其他</option>
                    </select>
                    <select id="dateFilter" class="px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-sky-500">
                        <option value="">所有时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                    </select>
                    <button id="exportSelectedBtn" class="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all">
                        <i class="fas fa-download mr-2"></i>导出选中
                    </button>
                </div>
            </div>
        </section>

        <!-- 结果列表 -->
        <section class="glass-card p-8 rounded-2xl shadow-2xl">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800 flex items-center">
                    <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-list text-white"></i>
                    </div>
                    识别结果列表
                </h3>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>共 <span id="totalCount" class="font-bold text-sky-600">0</span> 条结果</span>
                    <label class="flex items-center">
                        <input type="checkbox" id="selectAll" class="mr-2">
                        全选
                    </label>
                </div>
            </div>
            
            <div id="resultsList" class="space-y-4">
                <!-- 结果项将在此处动态添加 -->
                <div class="text-center py-16 text-gray-400">
                    <i class="fas fa-inbox fa-4x mb-4"></i>
                    <p class="text-lg">暂无识别结果</p>
                    <p class="text-sm mt-2">开始使用OCR识别功能来查看结果</p>
                </div>
            </div>

            <!-- 分页 -->
            <div id="pagination" class="flex justify-center items-center mt-8 space-x-2 hidden">
                <button class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="px-4 py-2 text-sm text-gray-600">第 1 页，共 1 页</span>
                <button class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg text-white text-center p-8 mt-16">
        <div class="container mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-sky-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search-plus text-white"></i>
                    </div>
                    <span class="text-xl font-bold">OCR智能识别</span>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="#" class="hover:text-sky-400 transition-colors">使用条款</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">隐私政策</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">联系我们</a>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-6">
                <p class="text-gray-300">&copy; 2024 OCR智能识别平台. 保留所有权利.</p>
                <p class="text-sm text-gray-400 mt-2">技术支持：Trae AI | 让AI为您的工作赋能</p>
            </div>
        </div>
    </footer>

    <script>
        // 模拟数据
        const mockResults = [
            {
                id: 1,
                fileName: "合同文档_001.jpg",
                category: "document",
                date: "2024-01-15",
                text: "甲方：北京科技有限公司\n乙方：上海贸易有限公司\n\n合同编号：HT2024001\n签订日期：2024年1月15日\n\n本合同经双方友好协商，就以下事项达成一致...",
                confidence: 98.5,
                selected: false
            },
            {
                id: 2,
                fileName: "发票_20240115.png",
                category: "receipt",
                date: "2024-01-15",
                text: "增值税专用发票\n\n发票代码：144001234567\n发票号码：12345678\n开票日期：2024年01月15日\n\n购买方：北京科技有限公司\n销售方：上海设备制造厂\n\n货物名称：办公设备\n规格型号：HP-2024\n数量：5台\n单价：2,500.00元\n金额：12,500.00元\n税率：13%\n税额：1,625.00元\n价税合计：14,125.00元",
                confidence: 99.2,
                selected: false
            }
        ];

        let currentResults = [...mockResults];
        let selectedResults = new Set();

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            renderResults();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterResults);
            document.getElementById('categoryFilter').addEventListener('change', filterResults);
            document.getElementById('dateFilter').addEventListener('change', filterResults);
            document.getElementById('selectAll').addEventListener('change', toggleSelectAll);
            document.getElementById('exportSelectedBtn').addEventListener('click', exportSelected);
        }

        function renderResults() {
            const resultsList = document.getElementById('resultsList');
            const totalCount = document.getElementById('totalCount');
            
            totalCount.textContent = currentResults.length;

            if (currentResults.length === 0) {
                resultsList.innerHTML = `
                    <div class="text-center py-16 text-gray-400">
                        <i class="fas fa-search fa-4x mb-4"></i>
                        <p class="text-lg">未找到匹配的结果</p>
                        <p class="text-sm mt-2">请尝试调整搜索条件</p>
                    </div>
                `;
                return;
            }

            resultsList.innerHTML = currentResults.map(result => `
                <div class="result-card p-6 border border-gray-200 rounded-xl hover:shadow-lg transition-all">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start space-x-4 flex-1">
                            <input type="checkbox" class="mt-1 result-checkbox" data-id="${result.id}" ${result.selected ? 'checked' : ''}>
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-2">
                                    <h4 class="text-lg font-semibold text-gray-800">${result.fileName}</h4>
                                    <span class="px-3 py-1 text-xs font-medium rounded-full ${getCategoryStyle(result.category)}">${getCategoryName(result.category)}</span>
                                    <span class="text-sm text-gray-500">${result.date}</span>
                                </div>
                                <p class="text-gray-600 text-sm leading-relaxed mb-3">${result.text.substring(0, 150)}${result.text.length > 150 ? '...' : ''}</p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span class="flex items-center">
                                        <i class="fas fa-chart-line mr-1 text-green-500"></i>
                                        识别率: ${result.confidence}%
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-file-alt mr-1 text-blue-500"></i>
                                        ${result.text.length} 字符
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-2 ml-4">
                            <button onclick="viewResult(${result.id})" class="px-3 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                            <button onclick="editResult(${result.id})" class="px-3 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button onclick="deleteResult(${result.id})" class="px-3 py-2 text-sm bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            // 添加复选框事件监听
            document.querySelectorAll('.result-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const id = parseInt(this.dataset.id);
                    if (this.checked) {
                        selectedResults.add(id);
                    } else {
                        selectedResults.delete(id);
                    }
                    updateSelectAllState();
                });
            });
        }

        function getCategoryStyle(category) {
            const styles = {
                document: 'bg-blue-100 text-blue-800',
                receipt: 'bg-green-100 text-green-800',
                card: 'bg-purple-100 text-purple-800',
                other: 'bg-gray-100 text-gray-800'
            };
            return styles[category] || styles.other;
        }

        function getCategoryName(category) {
            const names = {
                document: '文档',
                receipt: '票据',
                card: '证件',
                other: '其他'
            };
            return names[category] || '其他';
        }

        function filterResults() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;

            currentResults = mockResults.filter(result => {
                const matchesSearch = result.fileName.toLowerCase().includes(searchTerm) || 
                                    result.text.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || result.category === categoryFilter;
                const matchesDate = !dateFilter || checkDateFilter(result.date, dateFilter);
                
                return matchesSearch && matchesCategory && matchesDate;
            });

            renderResults();
        }

        function checkDateFilter(date, filter) {
            const resultDate = new Date(date);
            const today = new Date();
            
            switch(filter) {
                case 'today':
                    return resultDate.toDateString() === today.toDateString();
                case 'week':
                    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    return resultDate >= weekAgo;
                case 'month':
                    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                    return resultDate >= monthAgo;
                default:
                    return true;
            }
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.result-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                const id = parseInt(checkbox.dataset.id);
                if (selectAll.checked) {
                    selectedResults.add(id);
                } else {
                    selectedResults.delete(id);
                }
            });
        }

        function updateSelectAllState() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.result-checkbox');
            const checkedCount = document.querySelectorAll('.result-checkbox:checked').length;
            
            selectAll.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
        }

        function viewResult(id) {
            const result = mockResults.find(r => r.id === id);
            if (result) {
                alert(`查看结果：${result.fileName}\n\n${result.text}`);
            }
        }

        function editResult(id) {
            const result = mockResults.find(r => r.id === id);
            if (result) {
                const newText = prompt('编辑识别结果：', result.text);
                if (newText !== null) {
                    result.text = newText;
                    renderResults();
                    alert('结果已更新！');
                }
            }
        }

        function deleteResult(id) {
            if (confirm('确定要删除这个识别结果吗？')) {
                const index = mockResults.findIndex(r => r.id === id);
                if (index > -1) {
                    mockResults.splice(index, 1);
                    selectedResults.delete(id);
                    filterResults();
                    alert('结果已删除！');
                }
            }
        }

        function exportSelected() {
            if (selectedResults.size === 0) {
                alert('请先选择要导出的结果！');
                return;
            }

            const selectedData = mockResults.filter(r => selectedResults.has(r.id));
            let exportContent = `OCR识别结果导出\n导出时间：${new Date().toLocaleString()}\n\n`;
            
            selectedData.forEach((result, index) => {
                exportContent += `${index + 1}. 文件名：${result.fileName}\n`;
                exportContent += `   分类：${getCategoryName(result.category)}\n`;
                exportContent += `   日期：${result.date}\n`;
                exportContent += `   识别率：${result.confidence}%\n`;
                exportContent += `   内容：\n${result.text}\n\n`;
                exportContent += `${'='.repeat(50)}\n\n`;
            });

            const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `OCR结果导出_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        }
    </script>

</body>
</html>
