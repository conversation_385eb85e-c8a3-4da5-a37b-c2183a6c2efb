<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线识别 - OCR智能识别</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
            min-height: 100vh;
        }
        .upload-area {
            border: 3px dashed #cbd5e1;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        .upload-area.dragover {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
            transform: scale(1.02);
        }
        .upload-area:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #3b82f6;
        }
        .preview-image {
            max-height: 300px;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .result-item {
            border-bottom: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .result-item:hover {
            background: rgba(59, 130, 246, 0.05);
            transform: translateX(4px);
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-nav {
            background: rgba(17, 24, 39, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 导航栏 -->
    <nav class="glass-nav text-white p-6 shadow-2xl">
        <div class="container mx-auto flex justify-between items-center">
            <a href="index.html" class="text-3xl font-bold flex items-center hover:scale-105 transition-transform">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-search-plus text-sky-600 text-xl"></i>
                </div>
                OCR智能识别
            </a>
            <div class="flex items-center space-x-2">
                <a href="index.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="batch_recognition.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-folder-open mr-2"></i>批量任务
                </a>
                <a href="results_management.html" class="px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
                    <i class="fas fa-chart-line mr-2"></i>结果管理
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="min-h-screen">
        <!-- 页面标题区域 -->
        <section class="py-16 px-6">
            <div class="max-w-6xl mx-auto text-center">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">在线图片文字识别</h2>
                <p class="text-xl text-blue-100 leading-relaxed mb-8 max-w-3xl mx-auto">
                    上传图片，AI智能识别并提取其中的文字内容。支持多种格式，识别结果可编辑和导出。
                </p>
                <div class="flex flex-wrap justify-center items-center gap-8 text-blue-100">
                    <span class="flex items-center text-lg">
                        <i class="fas fa-bolt mr-3 text-yellow-300"></i>
                        秒级识别
                    </span>
                    <span class="flex items-center text-lg">
                        <i class="fas fa-language mr-3 text-green-300"></i>
                        多语言支持
                    </span>
                    <span class="flex items-center text-lg">
                        <i class="fas fa-edit mr-3 text-blue-300"></i>
                        结果可编辑
                    </span>
                </div>
            </div>
        </section>

        <!-- 内容区域 -->
        <div class="px-6 pb-16">
            <div class="max-w-6xl mx-auto">

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                    <!-- 左侧：图片上传与预览 -->
                    <div class="glass-card p-8 rounded-2xl shadow-2xl">
                        <h3 class="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-sky-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold">1</span>
                            </div>
                            上传图片
                        </h3>
                        <div id="uploadArea" class="upload-area p-12 text-center rounded-2xl cursor-pointer">
                            <input type="file" id="fileInput" multiple accept="image/*" class="hidden">
                            <div class="mb-4">
                                <i class="fas fa-cloud-upload-alt fa-4x text-sky-500 mb-4"></i>
                            </div>
                            <p class="text-white text-lg font-medium mb-2">点击此处或拖拽图片到这里</p>
                            <p class="text-blue-200 text-sm">支持 JPG, PNG, BMP, WEBP 等格式，最大 10MB</p>
                        </div>
                
                <div id="imagePreviewContainer" class="mt-8 hidden">
                    <h4 class="text-lg font-bold mb-4 text-gray-700 flex items-center">
                        <i class="fas fa-images mr-2 text-sky-500"></i>
                        图片预览
                    </h4>
                    <div class="grid grid-cols-2 gap-4">
                        <!-- 图片预览将在此处动态添加 -->
                    </div>
                </div>

                        <button id="startRecognitionBtn" class="mt-8 w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 disabled:hover:scale-100" disabled>
                            <i class="fas fa-magic mr-2"></i>开始AI识别
                        </button>
                    </div>

                    <!-- 右侧：识别结果 -->
                    <div class="glass-card p-8 rounded-2xl shadow-2xl">
                        <h3 class="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold">2</span>
                            </div>
                            识别结果
                        </h3>
                        <div id="recognitionResultArea" class="min-h-[300px] p-6 bg-gray-50 rounded-xl border border-gray-200">
                            <div id="placeholderText" class="text-gray-400 text-center py-16">
                                <i class="fas fa-file-alt fa-3x mb-4"></i>
                                <p class="text-lg">识别结果将在此显示...</p>
                                <p class="text-sm mt-2">上传图片并点击识别按钮开始</p>
                            </div>
                            <!-- 识别结果将在此处动态添加 -->
                        </div>
                        <button id="exportResultsBtn" class="mt-6 w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 ease-in-out hidden transform hover:scale-105">
                            <i class="fas fa-download mr-2"></i>导出结果 (TXT)
                        </button>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 结果展示模态框 (示例，实际交互需JS) -->
        <div id="resultModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-xl font-semibold text-sky-700">识别详情</h4>
                    <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700"><i class="fas fa-times fa-lg"></i></button>
                </div> 
                <div id="modalContent" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-700 mb-2">原始图片:</h5>
                        <img id="modalImage" src="#" alt="识别图片" class="w-full rounded border preview-image">
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-700 mb-2">识别文本:</h5>
                        <textarea id="modalText" rows="10" class="w-full p-2 border border-gray-300 rounded-md bg-gray-50" readonly></textarea>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg text-white text-center p-8">
        <div class="max-w-6xl mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-sky-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search-plus text-white"></i>
                    </div>
                    <span class="text-xl font-bold">OCR智能识别</span>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="#" class="hover:text-sky-400 transition-colors">使用条款</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">隐私政策</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">联系我们</a>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-6">
                <p class="text-gray-300">&copy; 2025 OCR智能识别平台-Design By XQJ. 保留所有权利.</p>
                <p class="text-sm text-gray-400 mt-2">技术支持：XQJ | 让AI为您的工作赋能</p>
            </div>
        </div>
    </footer>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        const startRecognitionBtn = document.getElementById('startRecognitionBtn');
        const recognitionResultArea = document.getElementById('recognitionResultArea');
        const placeholderText = document.getElementById('placeholderText');
        const exportResultsBtn = document.getElementById('exportResultsBtn');
        
        const resultModal = document.getElementById('resultModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const modalImage = document.getElementById('modalImage');
        const modalText = document.getElementById('modalText');

        let uploadedFiles = []; // 存储已上传的文件对象

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (event) => {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (event) => {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = event.dataTransfer.files;
            handleFiles(files);
        });
        fileInput.addEventListener('change', (event) => {
            const files = event.target.files;
            handleFiles(files);
        });

        function handleFiles(files) {
            if (!files.length) return;
            imagePreviewContainer.innerHTML = '<h4 class="text-lg font-medium mb-2 text-gray-700">图片预览:</h4>'; // Reset preview
            uploadedFiles = Array.from(files);
            
            uploadedFiles.forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const imgContainer = document.createElement('div');
                        imgContainer.className = 'mb-2 p-2 border rounded-md inline-block relative';
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.alt = file.name;
                        img.className = 'preview-image max-w-xs inline-block';
                        
                        const removeBtn = document.createElement('button');
                        removeBtn.innerHTML = '<i class="fas fa-times-circle text-red-500"></i>';
                        removeBtn.className = 'absolute top-1 right-1 bg-white rounded-full p-0.5 text-xs';
                        removeBtn.onclick = () => removeFile(index);

                        imgContainer.appendChild(img);
                        imgContainer.appendChild(removeBtn);
                        imagePreviewContainer.appendChild(imgContainer);
                    }
                    reader.readAsDataURL(file);
                }
            });

            imagePreviewContainer.classList.remove('hidden');
            startRecognitionBtn.disabled = false;
            recognitionResultArea.innerHTML = '<p id="placeholderText" class="text-gray-400 text-center py-10">识别结果将在此显示...</p>';
            exportResultsBtn.classList.add('hidden');
        }

        function removeFile(indexToRemove) {
            uploadedFiles.splice(indexToRemove, 1);
            // Re-render previews
            const currentFiles = [...uploadedFiles]; // Create a copy before clearing
            uploadedFiles = []; // Clear and re-populate to maintain correct indices
            imagePreviewContainer.innerHTML = '<h4 class="text-lg font-medium mb-2 text-gray-700">图片预览:</h4>';
            if (currentFiles.length > 0) {
                 handleFiles(new FileListItems(currentFiles)); // FileListItems is a helper for creating FileList
            } else {
                imagePreviewContainer.classList.add('hidden');
                startRecognitionBtn.disabled = true;
            }
        }
        
        // Helper to create FileList object for re-rendering
        function FileListItems (files) {
            let b = new ClipboardEvent("").clipboardData || new DataTransfer();
            for (let i = 0, len = files.length; i<len; i++) b.items.add(files[i]);
            return b.files;
        }

        startRecognitionBtn.addEventListener('click', () => {
            if (uploadedFiles.length === 0) {
                alert('请先上传图片！');
                return;
            }
            placeholderText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>正在识别中，请稍候...';
            recognitionResultArea.innerHTML = ''; // Clear previous results or placeholder
            recognitionResultArea.appendChild(placeholderText);
            exportResultsBtn.classList.add('hidden');

            // 模拟API调用和结果处理
            setTimeout(() => {
                recognitionResultArea.innerHTML = ''; // Clear loading
                if (uploadedFiles.length === 0) { // Check again in case files were removed during timeout
                     recognitionResultArea.innerHTML = '<p id="placeholderText" class="text-gray-400 text-center py-10">请先上传图片再开始识别。</p>';
                     return;
                }

                uploadedFiles.forEach((file, index) => {
                    const resultId = `result-${Date.now()}-${index}`;
                    const mockText = `这是图片 '${file.name}' 的模拟识别结果文本。\nUnsplash上的图片真好看！\nFont Awesome图标也很棒。\nTailwind CSS让布局变得简单。`;
                    
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item p-3 hover:bg-gray-100 cursor-pointer';
                    resultItem.onclick = () => showResultDetail(file, mockText);

                    const fileNameEl = document.createElement('p');
                    fileNameEl.className = 'font-medium text-sky-700';
                    fileNameEl.textContent = file.name;
                    
                    const textPreviewEl = document.createElement('p');
                    textPreviewEl.className = 'text-sm text-gray-600 truncate';
                    textPreviewEl.textContent = mockText.split('\n')[0]; // Show first line as preview

                    resultItem.appendChild(fileNameEl);
                    resultItem.appendChild(textPreviewEl);
                    recognitionResultArea.appendChild(resultItem);
                });
                exportResultsBtn.classList.remove('hidden');
                if (recognitionResultArea.innerHTML === '') { // If all files were removed during processing
                    recognitionResultArea.innerHTML = '<p id="placeholderText" class="text-gray-400 text-center py-10">没有可显示的识别结果。</p>';
                }
            }, 2000);
        });

        function showResultDetail(file, text) {
            const reader = new FileReader();
            reader.onload = (e) => {
                modalImage.src = e.target.result;
            }
            reader.readAsDataURL(file);
            modalText.value = text;
            resultModal.classList.remove('hidden');
        }

        closeModalBtn.addEventListener('click', () => {
            resultModal.classList.add('hidden');
        });
        
        // Close modal on escape key
        window.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && !resultModal.classList.contains('hidden')) {
                resultModal.classList.add('hidden');
            }
        });

        exportResultsBtn.addEventListener('click', () => {
            let fullText = "";
            const results = recognitionResultArea.querySelectorAll('.result-item');
            if (results.length === 0 && uploadedFiles.length > 0) {
                // This case might happen if recognition is clicked, then files are removed before timeout completes
                // Or if the mock results are not yet populated.
                // For this prototype, we'll assume results are based on current `uploadedFiles` for simplicity if DOM is empty.
                uploadedFiles.forEach(file => {
                    fullText += `图片: ${file.name}\n`;
                    fullText += `模拟识别文本: 这是图片 '${file.name}' 的模拟识别结果文本。\nUnsplash上的图片真好看！\nFont Awesome图标也很棒。\nTailwind CSS让布局变得简单。\n\n---\n\n`;
                });
            } else {
                // This is a simplified way to get text. In a real app, you'd store results properly.
                // For this prototype, we'll re-generate mock text if needed or use what's in modal if only one.
                // This part is tricky without proper state management for results.
                // Let's assume the modalText holds the latest detailed view if one was opened, or re-mock.
                if (uploadedFiles.length === 1 && modalText.value) {
                     fullText += `图片: ${uploadedFiles[0].name}\n`;
                     fullText += `识别文本:\n${modalText.value}\n\n---\n\n`;
                } else {
                     uploadedFiles.forEach(file => {
                        fullText += `图片: ${file.name}\n`;
                        fullText += `模拟识别文本: 这是图片 '${file.name}' 的模拟识别结果文本。\nUnsplash上的图片真好看！\nFont Awesome图标也很棒。\nTailwind CSS让布局变得简单。\n\n---\n\n`;
                    });
                }
            }

            if (!fullText.trim()) {
                alert("没有可导出的结果。");
                return;
            }

            const blob = new Blob([fullText], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'ocr_results.txt';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        });

    </script>

</body>
</html>