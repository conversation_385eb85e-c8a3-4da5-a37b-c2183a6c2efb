<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线识别 - OCR智能识别</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
            min-height: 100vh;
        }
        .upload-area {
            border: 3px dashed #cbd5e1;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        .upload-area.dragover {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
            transform: scale(1.02);
        }
        .upload-area:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #3b82f6;
        }
        .preview-image {
            max-height: 200px;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .result-item {
            border-bottom: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .result-item:hover {
            background: rgba(59, 130, 246, 0.05);
            transform: translateX(4px);
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-nav {
            background: rgba(17, 24, 39, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        /* 编辑器样式 */
        .result-text-editor {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            resize: vertical;
            transition: all 0.3s ease;
        }
        .result-text-editor:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 滚动条样式 */
        .result-text-display::-webkit-scrollbar,
        .result-text-editor::-webkit-scrollbar,
        #recognitionResultArea::-webkit-scrollbar {
            width: 6px;
        }
        .result-text-display::-webkit-scrollbar-track,
        .result-text-editor::-webkit-scrollbar-track,
        #recognitionResultArea::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .result-text-display::-webkit-scrollbar-thumb,
        .result-text-editor::-webkit-scrollbar-thumb,
        #recognitionResultArea::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .result-text-display::-webkit-scrollbar-thumb:hover,
        .result-text-editor::-webkit-scrollbar-thumb:hover,
        #recognitionResultArea::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 编辑控制按钮动画 */
        .edit-controls {
            transition: all 0.3s ease;
        }
        .edit-controls.hidden {
            opacity: 0;
            transform: translateY(-10px);
        }
        .edit-controls:not(.hidden) {
            opacity: 1;
            transform: translateY(0);
        }

        /* 图片缩放样式 */
        .image-zoom-container {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            background: #f8fafc;
        }
        .image-zoom-container img {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: zoom-in;
        }
        .image-zoom-container img:hover {
            transform: scale(1.05);
        }
        .image-zoom-container img.zoomed {
            cursor: zoom-out;
        }
        .image-zoom-container.zoomed {
            overflow: auto;
        }

        /* 图片预览容器滚动条样式 */
        #imagePreviewContainer::-webkit-scrollbar {
            width: 6px;
        }
        #imagePreviewContainer::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        #imagePreviewContainer::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        #imagePreviewContainer::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- 导航栏 -->
    <nav class="glass-nav text-white p-4 shadow-2xl">
        <div class="container mx-auto flex justify-between items-center">
            <a href="index.html" class="text-2xl font-bold flex items-center hover:scale-105 transition-transform">
                <div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-search-plus text-sky-600 text-lg"></i>
                </div>
                OCR智能识别
            </a>
            <div class="flex items-center space-x-2">
                <a href="index.html" class="px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center text-sm">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="batch_recognition.html" class="px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center text-sm">
                    <i class="fas fa-folder-open mr-2"></i>批量任务
                </a>
                <a href="results_management.html" class="px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center text-sm">
                    <i class="fas fa-chart-line mr-2"></i>结果管理
                </a>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="min-h-screen">
        <!-- 页面标题区域 - 压缩版本 -->
        <section class="py-8 px-6">
            <div class="max-w-6xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">在线图片文字识别</h2>
                <p class="text-lg text-blue-100 leading-relaxed mb-6 max-w-3xl mx-auto">
                    上传图片，AI智能识别并提取其中的文字内容。支持多种格式，识别结果可编辑和导出。
                </p>
                <div class="flex flex-wrap justify-center items-center gap-6 text-blue-100 text-sm">
                    <span class="flex items-center">
                        <i class="fas fa-bolt mr-2 text-yellow-300"></i>
                        秒级识别
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-language mr-2 text-green-300"></i>
                        多语言支持
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-edit mr-2 text-blue-300"></i>
                        结果可编辑
                    </span>
                </div>
            </div>
        </section>

        <!-- 内容区域 -->
        <div class="px-6 pb-8">
            <div class="max-w-6xl mx-auto">

                <!-- 上传区域 -->
                <div class="glass-card p-6 rounded-2xl shadow-2xl mb-6">
                    <h3 class="text-xl font-bold mb-4 text-gray-800 flex items-center">
                        <div class="w-7 h-7 bg-gradient-to-r from-sky-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-cloud-upload-alt text-white text-sm"></i>
                        </div>
                        上传图片
                    </h3>
                    <div id="uploadArea" class="upload-area p-8 text-center rounded-2xl cursor-pointer">
                        <input type="file" id="fileInput" multiple accept="image/*" class="hidden">
                        <div class="mb-3">
                            <i class="fas fa-cloud-upload-alt fa-3x text-sky-500 mb-3"></i>
                        </div>
                        <p class="text-white text-base font-medium mb-2">点击此处或拖拽图片到这里</p>
                        <p class="text-blue-200 text-sm">支持 JPG, PNG, BMP, WEBP 等格式，最大 10MB</p>
                    </div>

                    <div class="mt-4 flex justify-center gap-4">
                        <button id="startRecognitionBtn" class="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 disabled:hover:scale-100" disabled>
                            <i class="fas fa-magic mr-2"></i>开始AI识别
                        </button>
                        <button id="reSelectBtn" class="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 hidden">
                            <i class="fas fa-redo mr-2"></i>重新选择
                        </button>
                    </div>
                </div>

                <!-- 图片预览和识别结果对比区域 -->
                <div id="comparisonArea" class="hidden">
                    <!-- 上半部分：左右两个窗口 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- 左侧：图片预览 -->
                        <div class="glass-card p-6 rounded-2xl shadow-2xl h-fit">
                            <h3 class="text-xl font-bold mb-4 text-gray-800 flex items-center">
                                <div class="w-7 h-7 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-images text-white text-sm"></i>
                                </div>
                                图片预览
                            </h3>
                            <div id="imagePreviewContainer" class="min-h-[400px] max-h-[600px] overflow-y-auto p-4 bg-gray-50 rounded-xl border border-gray-200">
                                <div id="imagePlaceholderText" class="text-gray-400 text-center py-12">
                                    <i class="fas fa-images fa-2x mb-3"></i>
                                    <p class="text-base">图片预览将在此显示...</p>
                                    <p class="text-sm mt-2">请先上传图片文件</p>
                                </div>
                                <!-- 图片预览将在此处动态添加 -->
                            </div>
                        </div>

                        <!-- 右侧：识别结果 -->
                        <div class="glass-card p-6 rounded-2xl shadow-2xl h-fit">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-xl font-bold text-gray-800 flex items-center">
                                    <div class="w-7 h-7 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-alt text-white text-sm"></i>
                                    </div>
                                    识别结果
                                </h3>
                                <button id="editModeBtn" class="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors hidden">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                            </div>
                            <div id="recognitionResultArea" class="min-h-[400px] max-h-[600px] overflow-y-auto p-4 bg-gray-50 rounded-xl border border-gray-200">
                                <div id="placeholderText" class="text-gray-400 text-center py-12">
                                    <i class="fas fa-file-alt fa-2x mb-3"></i>
                                    <p class="text-base">识别结果将在此显示...</p>
                                    <p class="text-sm mt-2">点击识别按钮开始处理</p>
                                </div>
                                <!-- 识别结果将在此处动态添加 -->
                            </div>
                            <div class="mt-4">
                                <button id="saveChangesBtn" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 ease-in-out hidden transform hover:scale-105">
                                    <i class="fas fa-save mr-2"></i>保存修改
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 中间独立的导出按钮区域 -->
                    <div class="flex justify-center mt-8">
                        <button id="exportResultsBtn" class="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 ease-in-out hidden transform hover:scale-105 shadow-lg hover:shadow-xl">
                            <i class="fas fa-download mr-3"></i>导出识别结果
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结果展示模态框 (示例，实际交互需JS) -->
        <div id="resultModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-xl font-semibold text-sky-700">识别详情</h4>
                    <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700"><i class="fas fa-times fa-lg"></i></button>
                </div> 
                <div id="modalContent" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-700 mb-2">原始图片:</h5>
                        <img id="modalImage" src="#" alt="识别图片" class="w-full rounded border preview-image">
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-700 mb-2">识别文本:</h5>
                        <textarea id="modalText" rows="10" class="w-full p-2 border border-gray-300 rounded-md bg-gray-50" readonly></textarea>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 bg-opacity-90 backdrop-filter backdrop-blur-lg text-white text-center p-6">
        <div class="max-w-6xl mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-center mb-4">
                <div class="flex items-center mb-3 md:mb-0">
                    <div class="w-7 h-7 bg-sky-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-search-plus text-white text-sm"></i>
                    </div>
                    <span class="text-lg font-bold">OCR智能识别</span>
                </div>
                <div class="flex space-x-4 text-sm">
                    <a href="#" class="hover:text-sky-400 transition-colors">使用条款</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">隐私政策</a>
                    <a href="#" class="hover:text-sky-400 transition-colors">联系我们</a>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-4">
                <p class="text-gray-300 text-sm">&copy; 2025 OCR智能识别平台-Design By XQJ. 保留所有权利.</p>
                <p class="text-xs text-gray-400 mt-1">技术支持：XQJ | 让AI为您的工作赋能</p>
            </div>
        </div>
    </footer>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        const startRecognitionBtn = document.getElementById('startRecognitionBtn');
        const reSelectBtn = document.getElementById('reSelectBtn');
        const comparisonArea = document.getElementById('comparisonArea');
        const recognitionResultArea = document.getElementById('recognitionResultArea');
        const placeholderText = document.getElementById('placeholderText');
        const exportResultsBtn = document.getElementById('exportResultsBtn');
        const editModeBtn = document.getElementById('editModeBtn');
        const saveChangesBtn = document.getElementById('saveChangesBtn');

        const resultModal = document.getElementById('resultModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const modalImage = document.getElementById('modalImage');
        const modalText = document.getElementById('modalText');

        let uploadedFiles = []; // 存储已上传的文件对象
        let recognitionResults = []; // 存储识别结果
        let isEditMode = false; // 编辑模式标志

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (event) => {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (event) => {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = event.dataTransfer.files;
            handleFiles(files);
        });
        fileInput.addEventListener('change', (event) => {
            const files = event.target.files;
            handleFiles(files);
        });

        function handleFiles(files) {
            if (!files.length) return;
            imagePreviewContainer.innerHTML = ''; // Reset preview
            uploadedFiles = Array.from(files);

            uploadedFiles.forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const imgContainer = document.createElement('div');
                        imgContainer.className = 'relative mb-4 bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow';

                        const fileHeader = document.createElement('div');
                        fileHeader.className = 'flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200';

                        const fileName = document.createElement('div');
                        fileName.className = 'flex items-center';
                        fileName.innerHTML = `
                            <i class="fas fa-file-image text-blue-500 mr-2"></i>
                            <span class="text-sm font-medium text-gray-700 truncate">${file.name}</span>
                        `;

                        const removeBtn = document.createElement('button');
                        removeBtn.innerHTML = '<i class="fas fa-times text-white"></i>';
                        removeBtn.className = 'bg-red-500 hover:bg-red-600 rounded-full w-6 h-6 flex items-center justify-center text-xs transition-colors';
                        removeBtn.onclick = () => removeFile(index);

                        fileHeader.appendChild(fileName);
                        fileHeader.appendChild(removeBtn);

                        const imgWrapper = document.createElement('div');
                        imgWrapper.className = 'relative overflow-hidden bg-gray-50 cursor-zoom-in';
                        imgWrapper.style.height = '300px'; // 增加图片显示高度

                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.alt = file.name;
                        img.className = 'w-full h-full object-contain transition-transform duration-300 hover:scale-105';
                        img.style.transformOrigin = 'center center';

                        // 添加图片缩放功能
                        let isZoomed = false;
                        img.addEventListener('click', (event) => {
                            event.stopPropagation();
                            if (!isZoomed) {
                                img.style.transform = 'scale(1.8)';
                                img.style.cursor = 'zoom-out';
                                imgWrapper.style.cursor = 'zoom-out';
                                imgWrapper.style.overflow = 'auto';
                                isZoomed = true;
                            } else {
                                img.style.transform = 'scale(1)';
                                img.style.cursor = 'zoom-in';
                                imgWrapper.style.cursor = 'zoom-in';
                                imgWrapper.style.overflow = 'hidden';
                                isZoomed = false;
                            }
                        });

                        imgWrapper.appendChild(img);
                        imgContainer.appendChild(fileHeader);
                        imgContainer.appendChild(imgWrapper);
                        imagePreviewContainer.appendChild(imgContainer);
                    }
                    reader.readAsDataURL(file);
                }
            });

            comparisonArea.classList.remove('hidden');
            startRecognitionBtn.disabled = false;
            reSelectBtn.classList.remove('hidden');
            recognitionResultArea.innerHTML = '<div id="placeholderText" class="text-gray-400 text-center py-12"><i class="fas fa-file-alt fa-2x mb-3"></i><p class="text-base">识别结果将在此显示...</p><p class="text-sm mt-2">点击识别按钮开始处理</p></div>';
            exportResultsBtn.classList.add('hidden');
        }

        function removeFile(indexToRemove) {
            uploadedFiles.splice(indexToRemove, 1);
            // Re-render previews
            const currentFiles = [...uploadedFiles]; // Create a copy before clearing
            uploadedFiles = []; // Clear and re-populate to maintain correct indices
            imagePreviewContainer.innerHTML = '';
            if (currentFiles.length > 0) {
                 handleFiles(new FileListItems(currentFiles)); // FileListItems is a helper for creating FileList
            } else {
                comparisonArea.classList.add('hidden');
                startRecognitionBtn.disabled = true;
                reSelectBtn.classList.add('hidden');
            }
        }
        
        // Helper to create FileList object for re-rendering
        function FileListItems (files) {
            let b = new ClipboardEvent("").clipboardData || new DataTransfer();
            for (let i = 0, len = files.length; i<len; i++) b.items.add(files[i]);
            return b.files;
        }

        startRecognitionBtn.addEventListener('click', () => {
            if (uploadedFiles.length === 0) {
                alert('请先上传图片！');
                return;
            }
            recognitionResultArea.innerHTML = '<div class="text-gray-400 text-center py-12"><i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p class="text-base">正在识别中，请稍候...</p></div>';
            exportResultsBtn.classList.add('hidden');

            // 模拟API调用和结果处理
            setTimeout(() => {
                recognitionResultArea.innerHTML = ''; // Clear loading
                if (uploadedFiles.length === 0) { // Check again in case files were removed during timeout
                     recognitionResultArea.innerHTML = '<div class="text-gray-400 text-center py-12"><p class="text-base">请先上传图片再开始识别。</p></div>';
                     return;
                }

                recognitionResults = []; // 清空之前的结果
                uploadedFiles.forEach((file, index) => {
                    const resultId = `result-${Date.now()}-${index}`;
                    const mockText = `这是图片 '${file.name}' 的模拟识别结果文本。\n\n在这个美丽的图片中，我们可以看到许多精彩的内容。\n\n人工智能技术让文字识别变得如此简单和准确。\n\nOCR技术正在改变我们处理文档的方式。\n\n感谢您使用我们的智能识别服务！`;

                    // 存储识别结果
                    recognitionResults.push({
                        file: file,
                        text: mockText,
                        originalText: mockText
                    });

                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item p-4 border-b border-gray-200 hover:bg-blue-50 cursor-pointer transition-colors';
                    resultItem.dataset.index = index;
                    resultItem.onclick = () => showResultDetail(index);

                    const fileNameEl = document.createElement('div');
                    fileNameEl.className = 'flex items-center justify-between mb-2';
                    fileNameEl.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-file-image text-blue-500 mr-2"></i>
                            <span class="font-medium text-gray-800">${file.name}</span>
                        </div>
                        <button class="edit-text-btn px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors" onclick="event.stopPropagation(); editResultText(${index})">
                            <i class="fas fa-edit mr-1"></i>编辑
                        </button>
                    `;

                    const textPreviewEl = document.createElement('div');
                    textPreviewEl.className = 'text-sm text-gray-600 bg-gray-50 p-3 rounded border';
                    textPreviewEl.innerHTML = `
                        <div class="font-medium text-gray-700 mb-2">识别文本内容：</div>
                        <div class="result-text-content">
                            <div class="result-text-display bg-white p-3 rounded border min-h-[120px] max-h-[300px] overflow-y-auto whitespace-pre-wrap text-gray-800 leading-relaxed" data-index="${index}">${mockText}</div>
                            <textarea class="result-text-editor hidden w-full p-3 border rounded resize-none min-h-[120px] max-h-[300px] text-gray-800 leading-relaxed" data-index="${index}" placeholder="编辑识别文本...">${mockText}</textarea>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="text-xs text-blue-600">点击查看完整内容</div>
                            <div class="edit-controls hidden" data-index="${index}">
                                <button class="save-edit-btn px-2 py-1 bg-green-500 hover:bg-green-600 text-white text-xs rounded mr-1" onclick="event.stopPropagation(); saveEdit(${index})">
                                    <i class="fas fa-save mr-1"></i>保存
                                </button>
                                <button class="cancel-edit-btn px-2 py-1 bg-gray-500 hover:bg-gray-600 text-white text-xs rounded" onclick="event.stopPropagation(); cancelEdit(${index})">
                                    <i class="fas fa-times mr-1"></i>取消
                                </button>
                            </div>
                        </div>
                    `;

                    resultItem.appendChild(fileNameEl);
                    resultItem.appendChild(textPreviewEl);
                    recognitionResultArea.appendChild(resultItem);
                });
                exportResultsBtn.classList.remove('hidden');
                editModeBtn.classList.remove('hidden');
                if (recognitionResultArea.innerHTML === '') { // If all files were removed during processing
                    recognitionResultArea.innerHTML = '<div class="text-gray-400 text-center py-12"><p class="text-base">没有可显示的识别结果。</p></div>';
                }
            }, 2000);
        });

        function showResultDetail(index) {
            const result = recognitionResults[index];
            const reader = new FileReader();
            reader.onload = (e) => {
                modalImage.src = e.target.result;
            }
            reader.readAsDataURL(result.file);
            modalText.value = result.text;
            resultModal.classList.remove('hidden');
        }

        // 编辑单个结果文本 - 直接在结果区域编辑
        function editResultText(index) {
            const displayEl = document.querySelector(`.result-text-display[data-index="${index}"]`);
            const editorEl = document.querySelector(`.result-text-editor[data-index="${index}"]`);
            const controlsEl = document.querySelector(`.edit-controls[data-index="${index}"]`);

            if (displayEl && editorEl && controlsEl) {
                // 切换到编辑模式
                displayEl.classList.add('hidden');
                editorEl.classList.remove('hidden');
                controlsEl.classList.remove('hidden');

                // 聚焦到编辑器
                editorEl.focus();

                // 调整编辑器高度以适应内容
                editorEl.style.height = 'auto';
                editorEl.style.height = Math.max(120, editorEl.scrollHeight) + 'px';
            }
        }

        // 保存编辑
        function saveEdit(index) {
            const displayEl = document.querySelector(`.result-text-display[data-index="${index}"]`);
            const editorEl = document.querySelector(`.result-text-editor[data-index="${index}"]`);
            const controlsEl = document.querySelector(`.edit-controls[data-index="${index}"]`);

            if (displayEl && editorEl && controlsEl) {
                const newText = editorEl.value;

                // 更新识别结果数据
                if (recognitionResults[index]) {
                    recognitionResults[index].text = newText;
                }

                // 更新显示内容
                displayEl.textContent = newText;

                // 切换回显示模式
                displayEl.classList.remove('hidden');
                editorEl.classList.add('hidden');
                controlsEl.classList.add('hidden');

                // 显示保存成功提示
                showSaveSuccess(index);
            }
        }

        // 取消编辑
        function cancelEdit(index) {
            const displayEl = document.querySelector(`.result-text-display[data-index="${index}"]`);
            const editorEl = document.querySelector(`.result-text-editor[data-index="${index}"]`);
            const controlsEl = document.querySelector(`.edit-controls[data-index="${index}"]`);

            if (displayEl && editorEl && controlsEl) {
                // 恢复原始内容
                if (recognitionResults[index]) {
                    editorEl.value = recognitionResults[index].text;
                }

                // 切换回显示模式
                displayEl.classList.remove('hidden');
                editorEl.classList.add('hidden');
                controlsEl.classList.add('hidden');
            }
        }

        // 显示保存成功提示
        function showSaveSuccess(index) {
            const saveBtn = document.querySelector(`.edit-controls[data-index="${index}"] .save-edit-btn`);
            if (saveBtn) {
                const originalText = saveBtn.innerHTML;
                saveBtn.innerHTML = '<i class="fas fa-check mr-1"></i>已保存';
                saveBtn.className = saveBtn.className.replace('bg-green-500 hover:bg-green-600', 'bg-green-600');

                setTimeout(() => {
                    saveBtn.innerHTML = originalText;
                    saveBtn.className = saveBtn.className.replace('bg-green-600', 'bg-green-500 hover:bg-green-600');
                }, 1500);
            }
        }

        // 更新结果显示
        function updateResultDisplay(index) {
            const displayEl = document.querySelector(`.result-text-display[data-index="${index}"]`);
            const editorEl = document.querySelector(`.result-text-editor[data-index="${index}"]`);
            const result = recognitionResults[index];

            if (displayEl && result) {
                displayEl.textContent = result.text;
            }
            if (editorEl && result) {
                editorEl.value = result.text;
            }
        }

        // 编辑模式切换
        editModeBtn.addEventListener('click', () => {
            isEditMode = !isEditMode;
            if (isEditMode) {
                enterEditMode();
            } else {
                exitEditMode();
            }
        });

        function enterEditMode() {
            editModeBtn.innerHTML = '<i class="fas fa-times mr-1"></i>退出编辑';
            editModeBtn.className = 'px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-sm rounded-lg transition-colors';
            saveChangesBtn.classList.remove('hidden');

            // 显示所有编辑按钮
            document.querySelectorAll('.edit-text-btn').forEach(btn => {
                btn.style.display = 'inline-block';
            });
        }

        function exitEditMode() {
            editModeBtn.innerHTML = '<i class="fas fa-edit mr-1"></i>编辑';
            editModeBtn.className = 'px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors';
            saveChangesBtn.classList.add('hidden');

            // 隐藏所有编辑按钮
            document.querySelectorAll('.edit-text-btn').forEach(btn => {
                btn.style.display = 'none';
            });
        }

        // 保存修改
        saveChangesBtn.addEventListener('click', () => {
            let hasChanges = false;
            recognitionResults.forEach(result => {
                if (result.text !== result.originalText) {
                    hasChanges = true;
                }
            });

            if (hasChanges) {
                alert('修改已保存！');
                // 更新原始文本为当前文本
                recognitionResults.forEach(result => {
                    result.originalText = result.text;
                });
            } else {
                alert('没有检测到修改。');
            }

            exitEditMode();
        });

        // 重新选择按钮功能
        reSelectBtn.addEventListener('click', () => {
            uploadedFiles = [];
            recognitionResults = [];
            isEditMode = false;
            fileInput.value = '';
            comparisonArea.classList.add('hidden');
            startRecognitionBtn.disabled = true;
            reSelectBtn.classList.add('hidden');
            exportResultsBtn.classList.add('hidden');
            editModeBtn.classList.add('hidden');
            saveChangesBtn.classList.add('hidden');

            // 重置图片预览区域
            imagePreviewContainer.innerHTML = `
                <div id="imagePlaceholderText" class="text-gray-400 text-center py-12">
                    <i class="fas fa-images fa-2x mb-3"></i>
                    <p class="text-base">图片预览将在此显示...</p>
                    <p class="text-sm mt-2">请先上传图片文件</p>
                </div>
            `;

            // 重置上传区域
            uploadArea.innerHTML = `
                <input type="file" id="fileInput" multiple accept="image/*" class="hidden">
                <div class="mb-3">
                    <i class="fas fa-cloud-upload-alt fa-3x text-sky-500 mb-3"></i>
                </div>
                <p class="text-white text-base font-medium mb-2">点击此处或拖拽图片到这里</p>
                <p class="text-blue-200 text-sm">支持 JPG, PNG, BMP, WEBP 等格式，最大 10MB</p>
            `;

            // 重新绑定事件
            const newFileInput = document.getElementById('fileInput');
            newFileInput.addEventListener('change', (event) => {
                const files = event.target.files;
                handleFiles(files);
            });
        });

        closeModalBtn.addEventListener('click', () => {
            resultModal.classList.add('hidden');
        });

        // Close modal on escape key
        window.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && !resultModal.classList.contains('hidden')) {
                resultModal.classList.add('hidden');
            }
        });

        exportResultsBtn.addEventListener('click', () => {
            if (recognitionResults.length === 0) {
                alert("没有可导出的结果。");
                return;
            }

            let fullText = "";
            const currentTime = new Date().toLocaleString('zh-CN');
            fullText += `OCR识别结果导出\n导出时间: ${currentTime}\n\n`;
            fullText += "=" * 50 + "\n\n";

            recognitionResults.forEach((result, index) => {
                fullText += `图片 ${index + 1}: ${result.file.name}\n`;
                fullText += `文件大小: ${(result.file.size / 1024).toFixed(2)} KB\n`;
                fullText += `识别文本:\n`;
                fullText += "-" * 30 + "\n";
                fullText += `${result.text}\n`;
                fullText += "-" * 30 + "\n\n";

                if (result.text !== result.originalText) {
                    fullText += `注: 此文本已被用户编辑修改\n\n`;
                }

                fullText += "=" * 50 + "\n\n";
            });

            fullText += `共识别 ${recognitionResults.length} 张图片\n`;
            fullText += `技术支持: OCR智能识别平台-Design By XQJ\n`;

            const blob = new Blob([fullText], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `OCR识别结果_${new Date().toISOString().slice(0,10)}.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);

            // 显示导出成功提示
            const originalText = exportResultsBtn.innerHTML;
            exportResultsBtn.innerHTML = '<i class="fas fa-check mr-2"></i>导出成功';
            exportResultsBtn.className = exportResultsBtn.className.replace('from-green-500 to-emerald-600', 'from-green-600 to-green-700');
            setTimeout(() => {
                exportResultsBtn.innerHTML = originalText;
                exportResultsBtn.className = exportResultsBtn.className.replace('from-green-600 to-green-700', 'from-green-500 to-emerald-600');
            }, 2000);
        });

    </script>

</body>
</html>